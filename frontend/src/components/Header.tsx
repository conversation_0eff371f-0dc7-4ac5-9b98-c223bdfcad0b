import React, { useState } from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Avatar,
  Chip,
  CircularProgress
} from '@mui/material';
import {
  Logout as LogoutIcon,
  Psychology as AIIcon,
  Schedule as HistoryIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import HistoryModal from './HistoryModal';

const Header: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [historyOpen, setHistoryOpen] = useState(false);

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
      navigate('/login');
    } finally {
      setIsLoggingOut(false);
    }
  };

  const handleTitleClick = () => {
    navigate('/');
  };

  const handleHistoryClick = () => {
    setHistoryOpen(true);
  };

  return (
    <>
      <AppBar position="static" sx={{ background: '#343a40' }}> {/* Trending indigo color */}
        <Toolbar>
          <AIIcon sx={{ mr: 1 }} />
          <Typography 
            variant="h6" 
            component="div" 
            sx={{ 
              flexGrow: 1, 
              fontWeight: 'bold',
              cursor: 'pointer',
              '&:hover': {
                opacity: 0.8
              }
            }}
            onClick={handleTitleClick}
          >
            Mergen Code
          </Typography>
          <IconButton
            color="inherit"
            onClick={handleHistoryClick}
            sx={{ 
              mr: 1,
              '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' }
            }}
          >
            <HistoryIcon />
          </IconButton>
          <Chip
            avatar={<Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)' }}>{user?.email?.[0]?.toUpperCase()}</Avatar>}
            label={user?.email}
            variant="outlined"
            sx={{ color: 'white', borderColor: 'rgba(255,255,255,0.3)', mr: 2 }}
          />
          <IconButton
            color="inherit"
            onClick={handleLogout}
            disabled={isLoggingOut}
            sx={{ '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' } }}
          >
            {isLoggingOut ? <CircularProgress size={24} color="inherit" /> : <LogoutIcon />}
          </IconButton>
        </Toolbar>
      </AppBar>

      <HistoryModal 
        open={historyOpen} 
        onClose={() => setHistoryOpen(false)} 
      />
    </>
  );
};

export default Header; 