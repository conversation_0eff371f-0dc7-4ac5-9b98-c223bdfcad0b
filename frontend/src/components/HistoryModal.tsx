import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  IconButton,
  Typography,
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  CircularProgress,
  Alert,
  Button,
  Chip,
  Divider
} from '@mui/material';
import {
  Close as CloseIcon,
  Build as BuildIcon,
  Schedule as ScheduleIcon,
  Launch as LaunchIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { interviewAPI } from '../utils/api';

interface HistoryModalProps {
  open: boolean;
  onClose: () => void;
}

interface HistoryItem {
  uuid: string;
  prompt: string;
  createdAt: string;
  updatedAt: string;
}

const HistoryModal: React.FC<HistoryModalProps> = ({ open, onClose }) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [history, setHistory] = useState<HistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (open && user?.id) {
      fetchHistory();
    }
  }, [open, user?.id]);

  const fetchHistory = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await interviewAPI.getUserHistory(user.id, 20);
      if (response.success) {
        setHistory(response.data);
      } else {
        setError('Failed to load history');
      }
    } catch (error: any) {
      console.error('Error fetching history:', error);
      setError(error.response?.data?.message || 'Failed to load history');
    } finally {
      setIsLoading(false);
    }
  };

  const handleProjectClick = (uuid: string) => {
    navigate(`/build/${uuid}`);
    onClose();
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    } else if (diffInHours < 168) {
      const days = Math.floor(diffInHours / 24);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          maxHeight: '80vh'
        }
      }}
    >
      <DialogTitle
        sx={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          pb: 2
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <ScheduleIcon sx={{ mr: 1 }} />
          <Typography variant="h6" fontWeight="bold">
            Project History
          </Typography>
        </Box>
        <IconButton
          onClick={onClose}
          sx={{ 
            color: 'white',
            '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' }
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        {isLoading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress sx={{ color: '#667eea' }} />
          </Box>
        )}

        {error && (
          <Box sx={{ p: 3 }}>
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
            <Button
              variant="contained"
              onClick={fetchHistory}
              sx={{
                bgcolor: '#667eea',
                '&:hover': { bgcolor: '#5a6fd8' }
              }}
            >
              Retry
            </Button>
          </Box>
        )}

        {!isLoading && !error && history.length === 0 && (
          <Box sx={{ textAlign: 'center', p: 4 }}>
            <BuildIcon sx={{ fontSize: 48, color: '#ccc', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
              No Projects Yet
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Create your first project to see it here
            </Typography>
          </Box>
        )}

        {!isLoading && !error && history.length > 0 && (
          <List sx={{ p: 0 }}>
            {history.map((item, index) => (
              <React.Fragment key={item.uuid}>
                <ListItem
                  sx={{
                    cursor: 'pointer',
                    '&:hover': {
                      backgroundColor: 'rgba(102, 126, 234, 0.05)'
                    },
                    py: 2,
                    px: 3
                  }}
                  onClick={() => handleProjectClick(item.uuid)}
                >
                  <ListItemText
                    primary={
                      <Typography
                        variant="body1"
                        sx={{
                          fontWeight: 500,
                          mb: 0.5,
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden'
                        }}
                      >
                        {item.prompt}
                      </Typography>
                    }
                    secondary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                        <Chip
                          size="small"
                          label={formatDate(item.updatedAt)}
                          sx={{
                            backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            color: '#667eea',
                            fontSize: '0.75rem'
                          }}
                        />
                        <Typography variant="caption" color="text.secondary">
                          Created: {new Date(item.createdAt).toLocaleDateString()}
                        </Typography>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleProjectClick(item.uuid);
                      }}
                      sx={{
                        color: '#667eea',
                        '&:hover': {
                          backgroundColor: 'rgba(102, 126, 234, 0.1)'
                        }
                      }}
                    >
                      <LaunchIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
                {index < history.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default HistoryModal; 