import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Alert,
  CircularProgress,
  Chip,
  Divider,
  Paper
} from '@mui/material';
import {
  Folder as FolderIcon,
  InsertDriveFile as FileIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Upload as UploadIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { sphereEngineAPI, WorkspaceFile, WorkspaceFileList } from '../utils/sphereEngineApi';

interface FileManagerProps {
  workspaceId: string;
  onFileSelect?: (file: WorkspaceFile) => void;
  onFileModified?: (filepath: string, content: string) => void;
  height?: string | number;
}

interface FileEditDialog {
  open: boolean;
  file: WorkspaceFile | null;
  isNew: boolean;
}

export const FileManager: React.FC<FileManagerProps> = ({
  workspaceId,
  onFileSelect,
  onFileModified,
  height = '400px'
}) => {
  const [files, setFiles] = useState<Array<{
    name?: string;
    path: string;
    type: 'file' | 'directory';
    size?: number;
    lastModified?: string;
  }> | null>(null);
  const [currentPath, setCurrentPath] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [editDialog, setEditDialog] = useState<FileEditDialog>({
    open: false,
    file: null,
    isNew: false
  });
  const [newFileName, setNewFileName] = useState('');
  const [fileContent, setFileContent] = useState('');

  // Load files from workspace
  const loadFiles = async (path: string = '') => {
    if (!workspaceId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await sphereEngineAPI.listWorkspaceFiles(workspaceId, path);
      if (response.success) {
        setFiles(response.files || response.data || []);
        setCurrentPath(path);

        // Show info message if this is mock data
        if (response.message && response.message.includes('mock data')) {
          setError('Note: Showing sample files. Sphere Engine API does not support file browsing. You can upload files and they will be available in the workspace.');
        }
      } else {
        setError(response.message || 'Failed to load files');
      }
    } catch (err: any) {
      if (err.message.includes('405') || err.message.includes('Method Not Allowed')) {
        setError('File browsing is not supported by Sphere Engine API. You can upload files using the upload section.');
        // Set some default files to show the interface
        setFiles([
          {
            path: 'HELLO.md',
            type: 'file',
            size: 45,
            lastModified: new Date().toISOString(),
            content: ''
          },
          {
            path: 'README.md',
            type: 'file',
            size: 234,
            lastModified: new Date().toISOString(),
            content: ''
          }
        ]);
      } else {
        setError(err.message || 'Failed to load files');
      }
    } finally {
      setLoading(false);
    }
  };

  // Load file content for editing
  const loadFileContent = async (filepath: string) => {
    try {
      const response = await sphereEngineAPI.getFileFromWorkspace(workspaceId, filepath);
      if (response.success) {
        return response.data.content;
      } else {
        throw new Error(response.message || 'Failed to load file content');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load file content');
      return '';
    }
  };

  // Handle file edit
  const handleEditFile = async (filepath: string) => {
    const content = await loadFileContent(filepath);
    setEditDialog({
      open: true,
      file: { path: filepath, content },
      isNew: false
    });
    setFileContent(content);
  };

  // Handle new file creation
  const handleNewFile = () => {
    setEditDialog({
      open: true,
      file: null,
      isNew: true
    });
    setNewFileName('');
    setFileContent('');
  };

  // Save file changes
  const handleSaveFile = async () => {
    if (!editDialog.file && !editDialog.isNew) return;

    try {
      const filepath = editDialog.isNew 
        ? (currentPath ? `${currentPath}/${newFileName}` : newFileName)
        : editDialog.file!.path;

      if (editDialog.isNew) {
        await sphereEngineAPI.createFileInWorkspace(workspaceId, filepath, fileContent);
      } else {
        await sphereEngineAPI.modifyFileInWorkspace(workspaceId, filepath, fileContent);
      }

      // Notify parent component
      onFileModified?.(filepath, fileContent);

      // Close dialog and refresh files
      setEditDialog({ open: false, file: null, isNew: false });
      await loadFiles(currentPath);

    } catch (err: any) {
      setError(err.message || 'Failed to save file');
    }
  };

  // Handle file selection
  const handleFileClick = async (filepath: string, isDirectory: boolean) => {
    if (isDirectory) {
      await loadFiles(filepath);
    } else {
      const content = await loadFileContent(filepath);
      onFileSelect?.({ path: filepath, content });
    }
  };

  // Navigate to parent directory
  const handleNavigateUp = () => {
    const parentPath = currentPath.split('/').slice(0, -1).join('/');
    loadFiles(parentPath);
  };

  // Get file extension for icon
  const getFileIcon = (filename: string, isDirectory: boolean) => {
    if (isDirectory) {
      return <FolderIcon color="primary" />;
    }
    return <FileIcon color="action" />;
  };

  // Get file type chip
  const getFileTypeChip = (filename: string) => {
    const extension = filename.split('.').pop()?.toLowerCase();
    const typeColors: { [key: string]: 'primary' | 'secondary' | 'success' | 'warning' | 'error' } = {
      'js': 'warning',
      'ts': 'primary',
      'jsx': 'warning',
      'tsx': 'primary',
      'py': 'success',
      'java': 'error',
      'cpp': 'secondary',
      'c': 'secondary',
      'html': 'error',
      'css': 'primary',
      'md': 'secondary',
      'json': 'warning',
      'xml': 'secondary',
      'txt': 'secondary'
    };

    return (
      <Chip
        label={extension || 'file'}
        size="small"
        color={typeColors[extension || ''] || 'default'}
        variant="outlined"
      />
    );
  };

  // Load files on component mount
  useEffect(() => {
    if (workspaceId) {
      loadFiles();
    }
  }, [workspaceId]);

  return (
    <Paper elevation={2} sx={{ height, display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', justifyContent: 'between', alignItems: 'center', mb: 1 }}>
          <Typography variant="h6">File Manager</Typography>
          <Box>
            <IconButton onClick={() => loadFiles(currentPath)} disabled={loading}>
              <RefreshIcon />
            </IconButton>
            <Button
              startIcon={<AddIcon />}
              onClick={handleNewFile}
              size="small"
              variant="outlined"
              sx={{ ml: 1 }}
            >
              New File
            </Button>
          </Box>
        </Box>

        {/* Current path */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2" color="text.secondary">
            Path: /{currentPath || 'root'}
          </Typography>
          {currentPath && (
            <Button size="small" onClick={handleNavigateUp}>
              .. (up)
            </Button>
          )}
        </Box>
      </Box>

      {/* Error display */}
      {error && (
        <Alert severity="error" sx={{ m: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* File list */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : files ? (
          <List dense>
            {files.map((file, index) => (
              <ListItem
                key={index}
                button
                onClick={() => handleFileClick(file.path, file.type === 'directory')}
              >
                <ListItemIcon>
                  {getFileIcon(file.name || file.path, file.type === 'directory')}
                </ListItemIcon>
                <ListItemText
                  primary={file.name || file.path.split('/').pop() || file.path}
                  secondary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                      {file.type === 'file' && getFileTypeChip(file.name || file.path)}
                      {file.size && (
                        <Typography variant="caption" color="text.secondary">
                          {(file.size / 1024).toFixed(1)} KB
                        </Typography>
                      )}
                    </Box>
                  }
                />
                {file.type === 'file' && (
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditFile(file.path);
                      }}
                    >
                      <EditIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                )}
              </ListItem>
            ))}
            {files.files.length === 0 && (
              <ListItem>
                <ListItemText
                  primary="No files found"
                  secondary="This directory is empty"
                />
              </ListItem>
            )}
          </List>
        ) : (
          <Box sx={{ p: 2 }}>
            <Typography color="text.secondary">
              Select a workspace to view files
            </Typography>
          </Box>
        )}
      </Box>

      {/* File Edit Dialog */}
      <Dialog
        open={editDialog.open}
        onClose={() => setEditDialog({ open: false, file: null, isNew: false })}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {editDialog.isNew ? 'Create New File' : `Edit ${editDialog.file?.path}`}
        </DialogTitle>
        <DialogContent>
          {editDialog.isNew && (
            <TextField
              label="File Name"
              value={newFileName}
              onChange={(e) => setNewFileName(e.target.value)}
              fullWidth
              margin="normal"
              placeholder="e.g., main.py, index.html, README.md"
            />
          )}
          <TextField
            label="File Content"
            value={fileContent}
            onChange={(e) => setFileContent(e.target.value)}
            multiline
            rows={20}
            fullWidth
            margin="normal"
            variant="outlined"
            sx={{ fontFamily: 'monospace' }}
          />
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setEditDialog({ open: false, file: null, isNew: false })}
            startIcon={<CancelIcon />}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSaveFile}
            startIcon={<SaveIcon />}
            variant="contained"
            disabled={editDialog.isNew && !newFileName.trim()}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default FileManager;
