import React, { useState, useRef } from 'react';
import {
  Box,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Typography,
  Paper,
  Chip
} from '@mui/material';
import {
  Upload as UploadIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  CloudUpload as CloudUploadIcon
} from '@mui/icons-material';
import { sphereEngineAPI } from '../utils/sphereEngineApi';

interface FileUploadProps {
  workspaceId: string;
  onFileUploaded?: (filepath: string, content: string) => void;
  onError?: (error: string) => void;
}

interface UploadDialog {
  open: boolean;
  file: File | null;
  filepath: string;
  content: string;
  folder: string;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  workspaceId,
  onFileUploaded,
  onError
}) => {
  const [uploadDialog, setUploadDialog] = useState<UploadDialog>({
    open: false,
    file: null,
    filepath: '',
    content: '',
    folder: ''
  });
  const [uploading, setUploading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleFileSelect = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setUploadDialog({
        open: true,
        file,
        filepath: file.name,
        content,
        folder: ''
      });
      // Reset the input value after the file is processed
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    };
    reader.readAsText(file);
  };

  // Handle file input change
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // Handle drag and drop
  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
    
    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // Upload file to workspace
  const handleUpload = async () => {
    if (!uploadDialog.filepath || !workspaceId) return;

    // Combine folder and filepath for the full path
    const fullPath = uploadDialog.folder
      ? `${uploadDialog.folder.replace(/\/$/, '')}/${uploadDialog.filepath.replace(/^\//, '')}`
      : uploadDialog.filepath;

    setUploading(true);

    try {
      const response = await sphereEngineAPI.uploadFileToWorkspace(
        workspaceId,
        fullPath,
        uploadDialog.content
      );

      if (response.success) {
        onFileUploaded?.(uploadDialog.filepath, uploadDialog.content);
        handleDialogClose();
      } else {
        onError?.(response.message || 'Failed to upload file');
      }
    } catch (error: any) {
      onError?.(error.message || 'Failed to upload file');
    } finally {
      setUploading(false);
    }
  };

  // Handle dialog close
  const handleDialogClose = () => {
    setUploadDialog({ open: false, file: null, filepath: '', content: '', folder: '' });
    // Reset file input to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Get file type from extension
  const getFileType = (filename: string) => {
    const extension = filename.split('.').pop()?.toLowerCase();
    const types: { [key: string]: string } = {
      'js': 'JavaScript',
      'ts': 'TypeScript',
      'jsx': 'React JSX',
      'tsx': 'React TSX',
      'py': 'Python',
      'java': 'Java',
      'cpp': 'C++',
      'c': 'C',
      'html': 'HTML',
      'css': 'CSS',
      'md': 'Markdown',
      'json': 'JSON',
      'xml': 'XML',
      'txt': 'Text'
    };
    return types[extension || ''] || 'Unknown';
  };

  return (
    <>
      {/* File Upload Area */}
      <Paper
        elevation={dragOver ? 4 : 1}
        sx={{
          p: 3,
          border: dragOver ? '2px dashed #1976d2' : '2px dashed #ccc',
          backgroundColor: dragOver ? 'rgba(25, 118, 210, 0.04)' : 'transparent',
          textAlign: 'center',
          cursor: 'pointer',
          transition: 'all 0.3s ease'
        }}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <CloudUploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          Upload Files to Workspace
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Drag and drop files here, or click to select files
        </Typography>
        <Button
          variant="outlined"
          startIcon={<UploadIcon />}
          onClick={() => fileInputRef.current?.click()}
        >
          Choose Files
        </Button>
        <input
          ref={fileInputRef}
          id="file-input"
          type="file"
          hidden
          onChange={handleFileInputChange}
          accept=".js,.ts,.jsx,.tsx,.py,.java,.cpp,.c,.html,.css,.md,.json,.xml,.txt,.sh"
        />
        <Typography variant="caption" display="block" sx={{ mt: 1 }}>
          Supported: .js, .ts, .jsx, .tsx, .py, .java, .cpp, .c, .html, .css, .md, .json, .xml, .txt
        </Typography>
      </Paper>

      {/* Upload Dialog */}
      <Dialog
        open={uploadDialog.open}
        onClose={handleDialogClose}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Upload File to Workspace
        </DialogTitle>
        <DialogContent>
          {uploadDialog.file && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                File Information
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                <Chip label={getFileType(uploadDialog.file.name)} color="primary" size="small" />
                <Chip 
                  label={`${(uploadDialog.file.size / 1024).toFixed(1)} KB`} 
                  color="secondary" 
                  size="small" 
                />
              </Box>
            </Box>
          )}

          <TextField
            label="Folder Path (Optional)"
            value={uploadDialog.folder}
            onChange={(e) => setUploadDialog(prev => ({ ...prev, folder: e.target.value }))}
            fullWidth
            margin="normal"
            placeholder="e.g., src, docs, assets, src/components"
            helperText="Specify the folder where the file should be saved (leave empty for root)"
          />

          {/* Quick folder suggestions */}
          <Box sx={{ mt: 1, mb: 2 }}>
            <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
              Quick folders:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {['src', 'docs', 'assets', 'tests', 'config', 'src/components', 'src/utils'].map((folder) => (
                <Button
                  key={folder}
                  size="small"
                  variant="outlined"
                  onClick={() => setUploadDialog(prev => ({ ...prev, folder }))}
                  sx={{ fontSize: '0.75rem', py: 0.5, px: 1 }}
                >
                  {folder}
                </Button>
              ))}
              <Button
                size="small"
                variant="outlined"
                onClick={() => setUploadDialog(prev => ({ ...prev, folder: '' }))}
                sx={{ fontSize: '0.75rem', py: 0.5, px: 1 }}
              >
                Root
              </Button>
            </Box>
          </Box>

          <TextField
            label="File Name"
            value={uploadDialog.filepath}
            onChange={(e) => setUploadDialog(prev => ({ ...prev, filepath: e.target.value }))}
            fullWidth
            margin="normal"
            placeholder="e.g., main.py, index.html, README.md"
            helperText="The filename (without folder path)"
          />

          {/* Full path preview */}
          {(uploadDialog.folder || uploadDialog.filepath) && (
            <Box sx={{ mt: 1, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
              <Typography variant="caption" color="text.secondary">
                Full path in workspace:
              </Typography>
              <Typography variant="body2" sx={{ fontFamily: 'monospace', mt: 0.5 }}>
                {uploadDialog.folder
                  ? `${uploadDialog.folder.replace(/\/$/, '')}/${uploadDialog.filepath.replace(/^\//, '')}`
                  : uploadDialog.filepath || '(enter filename)'}
              </Typography>
            </Box>
          )}

          <TextField
            label="File Content"
            value={uploadDialog.content}
            onChange={(e) => setUploadDialog(prev => ({ ...prev, content: e.target.value }))}
            multiline
            rows={15}
            fullWidth
            margin="normal"
            variant="outlined"
            sx={{ fontFamily: 'monospace' }}
            helperText="You can edit the file content before uploading"
          />
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleDialogClose}
            startIcon={<CancelIcon />}
            disabled={uploading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleUpload}
            startIcon={uploading ? <CircularProgress size={20} /> : <SaveIcon />}
            variant="contained"
            disabled={!uploadDialog.filepath.trim() || uploading}
          >
            {uploading ? 'Uploading...' : 'Upload to Workspace'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default FileUpload;
