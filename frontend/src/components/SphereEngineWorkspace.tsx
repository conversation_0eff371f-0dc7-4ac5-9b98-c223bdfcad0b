import React, { useEffect, useRef, useState } from 'react';
import { Box, Typography, Alert, CircularProgress, Button } from '@mui/material';
import { PlayArrow as PlayIcon, Stop as StopIcon, Refresh as RefreshIcon } from '@mui/icons-material';
import { workspacePersistence } from '../utils/workspacePersistence';
import { sphereEngineAPI } from '../utils/sphereEngineApi';

interface SphereEngineWorkspaceProps {
  projectId?: string;
  interviewUuid?: string;
  workspaceId?: string; // Optional existing workspace ID
  onWorkspaceReady?: (workspaceId: string) => void;
  onError?: (error: string) => void;
  height?: string | number;
  minHeight?: string | number;
}

interface WorkspaceConfig {
  customerId: string;
  accessToken: string;
  apiEndpoint: string;
}

const SphereEngineWorkspace: React.FC<SphereEngineWorkspaceProps> = ({
  projectId,
  interviewUuid,
  workspaceId: providedWorkspaceId,
  onWorkspaceReady,
  onError,
  height = '100%',
  minHeight = '500px'
}) => {
  const workspaceRef = useRef<HTMLDivElement>(null);
  const [workspaceId, setWorkspaceId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSDKLoaded, setIsSDKLoaded] = useState(false);
  const [workspaceStatus, setWorkspaceStatus] = useState<'starting' | 'running' | 'stopped' | 'error'>('stopped');

  // Configuration - these should come from environment variables
  const config: WorkspaceConfig = {
    customerId: import.meta.env.VITE_SPHERE_ENGINE_CUSTOMER_ID || 'demo',
    accessToken: import.meta.env.VITE_SPHERE_ENGINE_ACCESS_TOKEN || '',
    apiEndpoint: import.meta.env.VITE_SPHERE_ENGINE_API_ENDPOINT || 'demo.containers.sphere-engine.com'
  };

  // Load Sphere Engine SDK
  useEffect(() => {
    if (typeof window !== 'undefined' && !document.getElementById('sphere-engine-jssdk')) {
      console.log('Loading Sphere Engine SDK from:', config.apiEndpoint);

      // Initialize SE globals BEFORE loading the script - following official documentation pattern
      window.SE_BASE = config.apiEndpoint;
      window.SE_HTTPS = true;
      window.SE_USE_APPEND_CHILD = true;
      window.SE = window.SE || [];

      console.log('🔧 SE globals configured:', {
        SE_BASE: window.SE_BASE,
        SE_HTTPS: window.SE_HTTPS,
        SE_USE_APPEND_CHILD: window.SE_USE_APPEND_CHILD
      });

      // Define SE.ready function as per documentation
      window.SE.ready = function(f: () => void) {
        if (document.readyState !== "loading" && document.readyState !== "interactive") {
          f();
        } else {
          window.addEventListener("load", f);
        }
      };

      const script = document.createElement('script');
      script.id = 'sphere-engine-jssdk'; // Add ID as per documentation
      script.src = `https://${config.apiEndpoint}/static/sdk/sdk.min.js`;
      script.onload = () => {
        console.log('✅ Sphere Engine SDK loaded successfully');
        console.log('🔧 SE object after load:', window.SE);
        console.log('🔧 SE methods available:', window.SE ? Object.keys(window.SE) : 'SE not available');
        setIsSDKLoaded(true);
      };
      script.onerror = () => {
        const errorMsg = `Failed to load Sphere Engine SDK from ${config.apiEndpoint}`;
        console.error(errorMsg);
        setError(errorMsg);
        onError?.(errorMsg);
      };

      // Insert script as per documentation pattern
      const firstScript = document.getElementsByTagName('script')[0];
      if (firstScript && firstScript.parentNode) {
        firstScript.parentNode.insertBefore(script, firstScript);
      } else {
        document.head.appendChild(script);
      }

      return () => {
        if (document.head.contains(script)) {
          document.head.removeChild(script);
        }
      };
    } else if (window.SE) {
      console.log('Sphere Engine SDK already loaded');
      setIsSDKLoaded(true);
    }
  }, [config.apiEndpoint]);

  // Check for existing workspace or create new one when SDK is loaded
  useEffect(() => {
    if (isSDKLoaded && !workspaceId) {
      if (!interviewUuid) {
        setError('Interview UUID is required for workspace creation. Please ensure you are accessing this page from a valid interview session.');
        return;
      }
      checkForExistingWorkspaceOrCreate();
    }
  }, [isSDKLoaded, projectId, workspaceId, interviewUuid]);

  const initializeExistingWorkspace = async (workspaceId: string, workspaceData: any) => {
    console.log('🔗 Connecting to existing workspace:', workspaceId);

    // Try SDK widget first, fallback to iframe if it fails
    console.log('🔧 Attempting to initialize existing workspace with SDK widget');
    try {
      initializeWorkspaceWidget(workspaceId);
    } catch (err) {
      console.warn('Widget initialization failed for existing workspace, using iframe fallback:', err);
      initializeFallbackIframe(workspaceData);
    }
  };

  const checkForExistingWorkspaceOrCreate = async () => {
    // If a workspace ID is provided, use it directly
    if (providedWorkspaceId) {
      console.log('🔍 Using provided workspace ID:', providedWorkspaceId);
      setWorkspaceId(providedWorkspaceId);
      setWorkspaceStatus('running');
      onWorkspaceReady?.(providedWorkspaceId);

      // Save to local persistence
      if (projectId) {
        workspacePersistence.saveWorkspace({
          id: providedWorkspaceId,
          projectId: projectId,
          status: 'running'
        });
      }

      // Initialize the workspace widget with the provided ID
      try {
        await initializeExistingWorkspace(providedWorkspaceId, { id: providedWorkspaceId });
      } catch (err) {
        console.warn('Widget initialization failed, using iframe fallback:', err);
        initializeFallbackIframe({ id: providedWorkspaceId });
      }

      setIsLoading(false);
      return;
    }

    if (!interviewUuid) {
      setError('Interview UUID is required for workspace creation');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Use the new getOrCreateWorkspace API that handles scoped workspace logic
      console.log('🔍 Getting or creating workspace for interview:', interviewUuid);

      const response = await sphereEngineAPI.getOrCreateWorkspace({
        projectId: projectId || '025a0cfd469e4dc49b5bfbccc29539c4',
        interviewUuid: interviewUuid
      });

      if (response.success && response.data) {
        console.log('✅ Workspace ready:', response.data);

        const workspaceData = response.data;
        setWorkspaceId(workspaceData.workspaceId || workspaceData.id);

        // Set status based on response
        if (workspaceData.isExisting) {
          setWorkspaceStatus('running');
          console.log('📁 Using existing workspace');
        } else {
          setWorkspaceStatus('starting');
          console.log('🆕 Created new workspace');
        }

        onWorkspaceReady?.(workspaceData.workspaceId || workspaceData.id);

        // Save to local persistence for faster future checks
        if (projectId) {
          workspacePersistence.saveWorkspace({
            id: workspaceData.workspaceId || workspaceData.id,
            projectId: projectId,
            status: workspaceData.status || 'starting'
          });
        }

        // Initialize the workspace widget
        try {
          if (workspaceData.isExisting) {
            await initializeExistingWorkspace(workspaceData.workspaceId || workspaceData.id, workspaceData);
          } else {
            initializeWorkspaceWidget(workspaceData.workspaceId || workspaceData.id);
          }
        } catch (err) {
          console.warn('Widget initialization failed, using iframe fallback:', err);
          initializeFallbackIframe(workspaceData);
        }

        setIsLoading(false);
      } else {
        throw new Error(response.message || 'Failed to get or create workspace');
      }

    } catch (error: any) {
      console.error('Error getting or creating workspace:', error);
      setError(error.message || 'Failed to initialize workspace');
      setIsLoading(false);
    }
  };

  const createWorkspace = async () => {
    setIsLoading(true);
    setError(null);

    // This function is now deprecated in favor of getOrCreateWorkspace
    // Redirect to the new scoped workspace creation
    await checkForExistingWorkspaceOrCreate();
  };

  const initializeWorkspaceWidget = (wsId: string) => {
    console.log('🚀 Initializing workspace widget for ID:', wsId);

    // Use SE.ready function as per documentation
    const initializeWidget = () => {
      try {
        console.log('🔧 Starting widget initialization...');
        console.log('🔧 SE object:', window.SE);
        console.log('🔧 SE type:', typeof window.SE);

        if (!window.SE) {
          throw new Error('Sphere Engine SDK not loaded');
        }

        if (typeof window.SE.workspace !== 'function') {
          console.log('🔧 SE.workspace type:', typeof window.SE.workspace);
          console.log('🔧 SE properties:', Object.getOwnPropertyNames(window.SE));
          throw new Error('SE.workspace is not a function');
        }

        // Wait for DOM element to be available with retries
        const waitForElement = (retries = 10) => {
          const domElement = document.querySelector('[data-id="mergen-workspace"]');
          console.log('🔧 DOM element found:', domElement);
          console.log('🔧 DOM element data-workspace:', domElement?.getAttribute('data-workspace'));

          if (!domElement) {
            if (retries > 0) {
              console.log(`🔧 DOM element not found, retrying... (${retries} attempts left)`);
              setTimeout(() => waitForElement(retries - 1), 500);
              return;
            } else {
              throw new Error('Workspace DOM element not found');
            }
          }

          // According to documentation: SE.workspace(id) where id is the data-id attribute value
          console.log('🔧 Calling SE.workspace with DOM element ID: mergen-workspace');
          console.log('🔧 Workspace ID for data-workspace attribute:', wsId);

          const workspace = window.SE.workspace('mergen-workspace');
          console.log('🔧 Workspace object returned:', workspace);
          console.log('🔧 Workspace object type:', typeof workspace);

          if (!workspace) {
            throw new Error('Failed to create workspace widget');
          }

          // According to documentation, workspace events are handled via workspace.events.subscribe
          if (workspace.events && typeof workspace.events.subscribe === 'function') {
            // Set up workspace event listeners using the documented API
            workspace.events.subscribe('afterScenarioExecution', (e: any) => {
              console.log('✅ Scenario executed:', e.data.scenario);
            });

            workspace.events.subscribe('afterScenarioExecutionExt', (e: any) => {
              console.log('✅ Scenario execution details:', e.data);
              setWorkspaceStatus('running');
            });

            // Set status to running after successful initialization
            setWorkspaceStatus('running');
            console.log('✅ Workspace widget initialized successfully');

            // Update persistence with running status
            if (wsId) {
              workspacePersistence.updateWorkspaceAccess(wsId, 'running');
            }
          } else {
            // Fallback: just set status to running
            console.log('Workspace events API not available, using fallback');
            setWorkspaceStatus('running');

            // Update persistence with running status
            if (wsId) {
              workspacePersistence.updateWorkspaceAccess(wsId, 'running');
            }
          }
        };

        // Start waiting for the DOM element
        waitForElement();

      } catch (err: any) {
        const errorMsg = `Failed to initialize workspace widget: ${err.message}`;
        console.error(errorMsg, err);
        console.log('🔄 Falling back to iframe method due to widget error');

        // Fallback to iframe method
        const workspaceData = { id: wsId };
        initializeFallbackIframe(workspaceData);
      }
    };

    // Use SE.ready function as documented
    if (window.SE && window.SE.ready) {
      window.SE.ready(initializeWidget);
    } else {
      // Fallback: try initializing after a delay
      setTimeout(initializeWidget, 2000);
    }
  };

  const initializeFallbackIframe = (workspace: any) => {
    if (!workspaceRef.current) return;

    console.log('🔄 Using iframe fallback for workspace:', workspace);
    console.log('🔄 Workspace ID:', workspace.id);
    console.log('🔄 Workspace URL:', workspace.workspace_url);
    console.log('🔄 Config API endpoint:', config.apiEndpoint);

    // Wait for the workspace div element to be available
    const waitForWorkspaceElement = (retries = 10) => {
      const workspaceElement = workspaceRef.current?.querySelector('[data-id="mergen-workspace"]');

      if (!workspaceElement) {
        if (retries > 0) {
          console.log(`🔄 Workspace element not found for iframe, retrying... (${retries} attempts left)`);
          setTimeout(() => waitForWorkspaceElement(retries - 1), 500);
          return;
        } else {
          console.error('❌ Could not find workspace element for iframe');
          setError('Could not find workspace element for iframe');
          return;
        }
      }

      // Found the workspace element, proceed with iframe creation
      // Create iframe directly to workspace URL
      const iframe = document.createElement('iframe');

      // Construct the workspace URL using the Sphere Engine format
      // The workspace URL should be in the format: https://containers.sphere-engine.com/workspace/{id}
      let workspaceUrl;

      if (workspace.workspace_url) {
        workspaceUrl = workspace.workspace_url;
      } else if (workspace.id) {
        // Use the configured API endpoint instead of hardcoded URL
        workspaceUrl = `https://${config.apiEndpoint}/workspace/${workspace.id}`;

        // Add access token if available
        if (config.accessToken) {
          workspaceUrl += `?access_token=${config.accessToken}`;
        }
      } else {
        console.error('❌ No workspace ID or URL available');
        setError('Invalid workspace data');
        return;
      }

      console.log('🌐 Loading workspace URL:', workspaceUrl);
      iframe.src = workspaceUrl;
      iframe.style.width = '100%';
      iframe.style.height = '100%';
      iframe.style.minHeight = typeof minHeight === 'string' ? minHeight : `${minHeight}px`;
      iframe.style.border = 'none';
      iframe.style.display = 'block';
      iframe.title = 'Workspace';
      iframe.setAttribute('allowfullscreen', 'true');
      iframe.setAttribute('scrolling', 'yes');

      // Replace the workspace element with the iframe
      workspaceElement.innerHTML = '';
      workspaceElement.appendChild(iframe);

      // Set status to running after iframe loads
      iframe.onload = () => {
        setWorkspaceStatus('running');
        console.log('✅ Iframe workspace loaded successfully');

        // Update persistence with running status
        if (workspace.id) {
          workspacePersistence.updateWorkspaceAccess(workspace.id, 'running');
        }
      };

      iframe.onerror = () => {
        setWorkspaceStatus('error');
        const errorMsg = 'Failed to load workspace iframe';
        setError(errorMsg);
        console.error('❌ Iframe loading failed for workspace:', workspace.id);

        // Update persistence with error status
        if (workspace.id) {
          workspacePersistence.updateWorkspaceAccess(workspace.id, 'error');
        }
      };
    };

    // Start waiting for the workspace element
    waitForWorkspaceElement();
  };

  const stopWorkspace = async () => {
    if (!workspaceId) return;

    try {
      // Use backend API instead of direct Sphere Engine API call
      await sphereEngineAPI.stopWorkspace(workspaceId);
      setWorkspaceStatus('stopped');

      // Update persistence with stopped status
      workspacePersistence.updateWorkspaceAccess(workspaceId, 'stopped');
    } catch (err: any) {
      console.error('Failed to stop workspace:', err);
    }
  };

  const restartWorkspace = async () => {
    if (workspaceId) {
      await stopWorkspace();

      // Remove from persistence since we're creating a new workspace
      workspacePersistence.removeWorkspace(workspaceId);

      setWorkspaceId(null);
      // This will trigger useEffect to create a new workspace
    }
  };

  const getStatusColor = () => {
    switch (workspaceStatus) {
      case 'running': return 'success';
      case 'starting': return 'warning';
      case 'error': return 'error';
      default: return 'info';
    }
  };

  const getStatusText = () => {
    switch (workspaceStatus) {
      case 'running': return 'Workspace is running';
      case 'starting': return 'Starting workspace...';
      case 'error': return 'Workspace error';
      case 'stopped': return 'Workspace stopped';
      default: return 'Initializing...';
    }
  };

  return (
    <Box sx={{ 
      height: height === '100%' ? '100%' : height, 
      display: 'flex', 
      flexDirection: 'column', 
      minHeight,
      flex: 1  // Allow it to grow within flex container
    }}>
      {/* Workspace Header */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          p: 1,
          backgroundColor: '#2d2d2d',
          borderBottom: '1px solid #444',
          flexShrink: 0  // Prevent header from shrinking
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2" sx={{ color: '#cccccc', fontWeight: 'bold' }}>
            Workspace
          </Typography>
          {workspaceStatus === 'starting' && <CircularProgress size={16} sx={{ color: 'orange' }} />}
        </Box>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Typography 
            variant="caption" 
            sx={{ 
              color: getStatusColor() === 'success' ? '#4caf50' : 
                     getStatusColor() === 'warning' ? '#ff9800' :
                     getStatusColor() === 'error' ? '#f44336' : '#2196f3',
              fontSize: '12px'
            }}
          >
            {getStatusText()}
          </Typography>
          
          {workspaceStatus === 'running' && (
            <Button
              size="small"
              onClick={stopWorkspace}
              startIcon={<StopIcon />}
              sx={{ 
                minWidth: 'auto',
                p: 0.5,
                color: '#cccccc',
                '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' }
              }}
            />
          )}
          
          {(workspaceStatus === 'stopped' || workspaceStatus === 'error') && (
            <Button
              size="small"
              onClick={workspaceStatus === 'error' ? restartWorkspace : createWorkspace}
              startIcon={workspaceStatus === 'error' ? <RefreshIcon /> : <PlayIcon />}
              sx={{ 
                minWidth: 'auto',
                p: 0.5,
                color: '#cccccc',
                '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' }
              }}
            />
          )}
        </Box>
      </Box>

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ m: 1, flexShrink: 0 }}>
          {error}
        </Alert>
      )}

      {/* Loading State */}
      {isLoading && (
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#1e1e1e',
            minHeight: typeof minHeight === 'string' ? minHeight : `${minHeight}px`
          }}
        >
          <Box sx={{ textAlign: 'center', color: '#cccccc' }}>
            <CircularProgress size={40} sx={{ mb: 2, color: '#667eea' }} />
            <Typography variant="body1">
              {workspaceId ? 'Connecting to workspace...' : 'Initializing workspace...'}
            </Typography>
          </Box>
        </Box>
      )}

      {/* Workspace Container */}
      <Box
        ref={workspaceRef}
        sx={{
          flex: 1,
          backgroundColor: '#1e1e1e',
          minHeight: typeof minHeight === 'string' ? minHeight : `${minHeight}px`,
          overflow: 'auto',  // Allow scrolling in the container
          position: 'relative',
          '& iframe': {
            width: '100%',
            height: '100%',
            minHeight: typeof minHeight === 'string' ? minHeight : `${minHeight}px`,
            border: 'none',
            display: 'block'
          },
          // Ensure the Sphere Engine widget container can scroll
          '& [data-id="mergen-workspace"]': {
            height: '100%',
            minHeight: typeof minHeight === 'string' ? minHeight : `${minHeight}px`,
            overflow: 'auto'
          }
        }}
      >
        {/* Sphere Engine Workspace Element */}
        {workspaceId && (
          <div
            data-id="mergen-workspace"
            data-workspace={workspaceId}
            style={{
              width: '100%',
              height: '100%',
              minHeight: typeof minHeight === 'string' ? minHeight : `${minHeight}px`
            }}
          />
        )}
      </Box>
    </Box>
  );
};

// Extend window type for Sphere Engine SDK
declare global {
  interface Window {
    SE: any;
    SE_BASE: string;
    SE_HTTPS: boolean;
    SE_USE_APPEND_CHILD: boolean;
  }
}

export default SphereEngineWorkspace; 