import React, { useState } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Alert,
  Chip,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Code as CodeIcon,
  RocketLaunch as RocketIcon,
  Computer as ComputerIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
  CloudUpload as CloudUploadIcon
} from '@mui/icons-material';
import Header from '../components/Header';
import SphereEngineWorkspace from '../components/SphereEngineWorkspace';
import FileUpload from '../components/FileUpload';
import { workspacePersistence } from '../utils/workspacePersistence';

const SphereEngineLiveCoding: React.FC = () => {
  const [workspaceId, setWorkspaceId] = useState<string | null>(null);
  const [workspaceError, setWorkspaceError] = useState<string | null>(null);
  const [isWorkspaceReady, setIsWorkspaceReady] = useState(false);
  const [showWorkspaceInfo, setShowWorkspaceInfo] = useState(false);
  const [workspaceStats, setWorkspaceStats] = useState<any>(null);

  // File management state
  const [showFileUpload, setShowFileUpload] = useState(false);

  const handleWorkspaceReady = (id: string) => {
    setWorkspaceId(id);
    setIsWorkspaceReady(true);
    setWorkspaceError(null);
  };

  const handleWorkspaceError = (error: string) => {
    setWorkspaceError(error);
    setIsWorkspaceReady(false);
  };

  const handleFileUploadError = (error: string) => {
    console.log('File upload error:', error);
  };

  const handleShowWorkspaceInfo = () => {
    const stats = workspacePersistence.getWorkspaceStats();
    const workspaces = workspacePersistence.getPersistedWorkspaces();
    setWorkspaceStats({ ...stats, workspaces });
    setShowWorkspaceInfo(true);
  };

  const handleClearWorkspaces = () => {
    workspacePersistence.clearAllWorkspaces();
    setWorkspaceStats(null);
    setShowWorkspaceInfo(false);
    // Refresh the page to test persistence
    window.location.reload();
  };

  return (
    <>
      <Header />
      
      <Box
        sx={{
          minHeight: 'calc(100vh - 64px)',
          background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
          p: 2
        }}
      >
        <Container maxWidth="xl">
          {/* Page Header */}
          <Paper
            elevation={6}
            sx={{
              borderRadius: 4,
              p: 3,
              mb: 3,
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <RocketIcon sx={{ fontSize: 40 }} />
              <Box>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                  Sphere Engine Live Coding Environment
                </Typography>
                <Typography variant="h6" sx={{ opacity: 0.9 }}>
                  Full-featured cloud IDE for instant development
                </Typography>
              </Box>
            </Box>

            {/* Status Chips and Controls */}
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', alignItems: 'center' }}>
              <Chip
                icon={<ComputerIcon />}
                label={isWorkspaceReady ? 'Workspace Ready' : 'Initializing...'}
                color={isWorkspaceReady ? 'success' : 'warning'}
                sx={{
                  bgcolor: isWorkspaceReady ? 'rgba(76, 175, 80, 0.2)' : 'rgba(255, 152, 0, 0.2)',
                  color: 'white',
                  '& .MuiChip-icon': { color: 'white' }
                }}
              />
              {workspaceId && (
                <Chip
                  icon={<CodeIcon />}
                  label={`ID: ${workspaceId.substring(0, 8)}...`}
                  sx={{
                    bgcolor: 'rgba(255, 255, 255, 0.2)',
                    color: 'white',
                    '& .MuiChip-icon': { color: 'white' }
                  }}
                />
              )}

              {/* Test Controls */}
              <Button
                variant="outlined"
                size="small"
                startIcon={<InfoIcon />}
                onClick={handleShowWorkspaceInfo}
                sx={{
                  color: 'white',
                  borderColor: 'rgba(255, 255, 255, 0.5)',
                  '&:hover': {
                    borderColor: 'white',
                    bgcolor: 'rgba(255, 255, 255, 0.1)'
                  }
                }}
              >
                Workspace Info
              </Button>

              <Button
                variant="outlined"
                size="small"
                startIcon={<RefreshIcon />}
                onClick={() => window.location.reload()}
                sx={{
                  color: 'white',
                  borderColor: 'rgba(255, 255, 255, 0.5)',
                  '&:hover': {
                    borderColor: 'white',
                    bgcolor: 'rgba(255, 255, 255, 0.1)'
                  }
                }}
              >
                Test Refresh
              </Button>
            </Box>
          </Paper>

       

          {/* Error Display */}
          {workspaceError && (
            <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
              <Typography variant="h6" sx={{ mb: 1 }}>
                Workspace Error
              </Typography>
              <Typography variant="body2">
                {workspaceError}
              </Typography>
            </Alert>
          )}

          {/* File Management Controls */}
          {isWorkspaceReady && workspaceId && (
            <Paper elevation={3} sx={{ p: 2, mb: 3, borderRadius: 2 }}>
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
                <Typography variant="h6" sx={{ mr: 2 }}>
                  File Management
                </Typography>

                <Button
                  variant={showFileUpload ? "contained" : "outlined"}
                  startIcon={<CloudUploadIcon />}
                  onClick={() => setShowFileUpload(!showFileUpload)}
                  size="small"
                >
                  Upload Files
                </Button>

                <Typography variant="caption" color="text.secondary" sx={{ ml: 'auto' }}>
                  Workspace ID: {workspaceId.substring(0, 8)}...
                </Typography>
              </Box>

              {/* Info about file upload */}
              {/* <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>File Upload:</strong> Upload files directly to your Sphere Engine workspace.
                  Files will be available in the workspace environment for your development work.
                </Typography>
              </Alert> */}
            </Paper>
          )}

          {/* File Upload Section */}
          {showFileUpload && isWorkspaceReady && workspaceId && (
            <Paper elevation={3} sx={{ p: 3, mb: 3, borderRadius: 2 }}>
              <Typography variant="h6" gutterBottom>
                Upload Files to Workspace
              </Typography>
              <FileUpload
                workspaceId={workspaceId}
                onError={handleFileUploadError}
              />
            </Paper>
          )}

          {/* Main Content Layout */}
          <Box sx={{ display: 'flex', gap: 3, height: 'calc(100vh - 500px)', minHeight: '600px' }}>
            {/* Workspace */}
            <Paper
              elevation={6}
              sx={{
                flex: 1,
                borderRadius: 4,
                overflow: 'hidden',
                display: 'flex',
                flexDirection: 'column'
              }}
            >
              <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider', bgcolor: 'primary.main', color: 'white' }}>
                <Typography variant="h6">
                  Workspace
                </Typography>
              </Box>
              <Box sx={{ flex: 1 }}>
                <SphereEngineWorkspace
                  projectId="025a0cfd469e4dc49b5bfbccc29539c4"
                  onWorkspaceReady={handleWorkspaceReady}
                  onError={handleWorkspaceError}
                  height="100%"
                  minHeight="500px"
                />
              </Box>
            </Paper>
          </Box>




        </Container>
      </Box>

      {/* Workspace Info Dialog */}
      <Dialog
        open={showWorkspaceInfo}
        onClose={() => setShowWorkspaceInfo(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Workspace Persistence Information
        </DialogTitle>
        <DialogContent>
          {workspaceStats && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Statistics
              </Typography>
              <Typography variant="body2" paragraph>
                Total Workspaces: {workspaceStats.total}
              </Typography>
              <Typography variant="body2" paragraph>
                Recently Accessed: {workspaceStats.recentlyAccessed}
              </Typography>
              <Typography variant="body2" paragraph>
                Expired: {workspaceStats.expired}
              </Typography>

              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Status Breakdown
              </Typography>
              {Object.entries(workspaceStats.byStatus).map(([status, count]) => (
                <Typography key={status} variant="body2">
                  {status}: {count as number}
                </Typography>
              ))}

              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Stored Workspaces
              </Typography>
              {workspaceStats.workspaces.length > 0 ? (
                workspaceStats.workspaces.map((workspace: any, index: number) => (
                  <Paper key={index} sx={{ p: 2, mb: 1, bgcolor: 'grey.50' }}>
                    <Typography variant="body2">
                      <strong>ID:</strong> {workspace.id}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Project ID:</strong> {workspace.projectId}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Status:</strong> {workspace.status}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Created:</strong> {new Date(workspace.createdAt).toLocaleString()}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Last Accessed:</strong> {new Date(workspace.lastAccessedAt).toLocaleString()}
                    </Typography>
                  </Paper>
                ))
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No workspaces stored
                </Typography>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClearWorkspaces} color="error">
            Clear All Workspaces
          </Button>
          <Button onClick={() => setShowWorkspaceInfo(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default SphereEngineLiveCoding;