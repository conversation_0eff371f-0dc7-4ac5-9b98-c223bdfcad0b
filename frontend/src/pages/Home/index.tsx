import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  TextField,
  CircularProgress,
  IconButton,
  Card,
  CardContent,
  CardActions,
  Button,
     
  Chip
} from '@mui/material';
import {
  Send as SendIcon,
  Launch as LaunchIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import Header from '../../components/Header';
import HistoryModal from '../../components/HistoryModal';
import { interviewAPI } from '../../utils/api';

interface RecentProject {
  uuid: string;
  prompt: string;
  createdAt: string;
  updatedAt: string;
}

const Home: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [prompt, setPrompt] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [recentProjects, setRecentProjects] = useState<RecentProject[]>([]);
  const [isLoadingProjects, setIsLoadingProjects] = useState(true);
  const [historyOpen, setHistoryOpen] = useState(false);

  useEffect(() => {
    if (user?.id) {
      fetchRecentProjects();
    }
  }, [user?.id]);

  const fetchRecentProjects = async () => {
    if (!user?.id) return;

    try {
      setIsLoadingProjects(true);
      const response = await interviewAPI.getUserHistory(user.id, 6); // Get 6 most recent
      if (response.success) {
        setRecentProjects(response.data);
      }
    } catch (error) {
      console.error('Error fetching recent projects:', error);
    } finally {
      setIsLoadingProjects(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim() || isSubmitting) return;

    setIsSubmitting(true);
    
    // TODO: This will be replaced with actual API call to backend
    // console.log('Submitted prompt:', prompt);
    
    // Simulate processing and navigate to interview page
    setTimeout(() => {
      const currentPrompt = prompt;
      setPrompt('');
      setIsSubmitting(false);
      // Navigate to interview page with the prompt data
      navigate('/interview', { state: { prompt: currentPrompt } });
    }, 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e as any);
    }
  };

  const handleProjectClick = (uuid: string) => {
    navigate(`/build/${uuid}`);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    } else if (diffInHours < 168) {
      const days = Math.floor(diffInHours / 24);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <>
      <Header />

      <Box
        sx={{
          minHeight: 'calc(100vh - 64px)',
          background: 'linear-gradient(135deg, #6c757d 0%, #dee2e6 100%)', // Trending pink to coral gradient
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          p: 4
        }}
      >
        {/* Hero Section */}
        <Box sx={{ textAlign: 'center', mb: 6, maxWidth: 800 }}>
          <Typography
            variant="h2"
            component="h1"
            sx={{
              color: 'white',
              fontWeight: 'bold',
              mb: 2,
              fontSize: { xs: '2.5rem', md: '3.5rem' },
              lineHeight: 1.2
            }}
          >
            Build something
            <br />
            with Mergen Code
          </Typography>
          <Typography
            variant="h5"
            sx={{
              color: 'rgba(255,255,255,0.8)',
              fontWeight: 300,
              mb: 4
            }}
          >
            Create apps and websites by chatting with AI
          </Typography>
        </Box>

        {/* Chat Input */}
        <Container maxWidth="md">
          <Paper
            elevation={6}
            sx={{
              borderRadius: 4,
              overflow: 'hidden',
              background: 'rgba(255,255,255,0.95)',
              backdropFilter: 'blur(10px)',
              mb: 4
            }}
          >
            <form onSubmit={handleSubmit}>
              <Box sx={{ display: 'flex', alignItems: 'flex-end' }}>
                <TextField
                  fullWidth
                  multiline
                  maxRows={6}
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask Mergen to create a prototype..."
                  variant="standard"
                  disabled={isSubmitting}
                  rows={3}
                  InputProps={{
                    disableUnderline: true,
                    sx: {
                      fontSize: '1.1rem',
                      p: 2,
                      minHeight: 60
                    }
                  }}
                />

                <IconButton
                  type="submit"
                  disabled={!prompt.trim() || isSubmitting}
                  sx={{
                    width: 48,
                    height: 48,
                    borderRadius: '50%',
                    bgcolor: '#667eea',
                    color: 'white',
                    m: 1,
                    '&:hover': {
                      bgcolor: '#5a6fd8'
                    },
                    '&:disabled': {
                      bgcolor: '#e0e0e0',
                      color: '#999'
                    }
                  }}
                >
                  {isSubmitting ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : (
                    <SendIcon fontSize="small" />
                  )}
                </IconButton>
              </Box>
            </form>
          </Paper>

          {/* Recent Projects */}
          {!isLoadingProjects && recentProjects.length > 0 && (
            <Paper
              elevation={6}
              sx={{
                borderRadius: 4,
                overflow: 'hidden',
                background: 'rgba(255,255,255,0.95)',
                backdropFilter: 'blur(10px)',
                p: 3
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ScheduleIcon sx={{ mr: 1, color: '#667eea' }} />
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 'bold',
                    color: '#667eea',
                    flexGrow: 1
                  }}
                >
                  Recent Projects
                </Typography>
                <Button
                  size="small"
                  onClick={() => setHistoryOpen(true)}
                  sx={{ color: '#667eea' }}
                >
                  View All
                </Button>
              </Box>

              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
                  gap: 2
                }}
              >
                {recentProjects.map((project) => (
                  <Card
                    key={project.uuid}
                    sx={{
                      cursor: 'pointer',
                      height: '100%',
                      transition: 'all 0.2s',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: 4
                      }
                    }}
                    onClick={() => handleProjectClick(project.uuid)}
                  >
                    <CardContent sx={{ pb: 1 }}>
                      <Typography
                        variant="body1"
                        sx={{
                          fontWeight: 500,
                          mb: 1,
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          minHeight: '48px'
                        }}
                      >
                        {project.prompt}
                      </Typography>
                      <Chip
                        size="small"
                        label={formatDate(project.updatedAt)}
                        sx={{
                          backgroundColor: 'rgba(102, 126, 234, 0.1)',
                          color: '#667eea',
                          fontSize: '0.75rem'
                        }}
                      />
                    </CardContent>
                    <CardActions sx={{ pt: 0, pb: 2, px: 2 }}>
                      <Button
                        size="small"
                        startIcon={<LaunchIcon />}
                        sx={{ color: '#667eea' }}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleProjectClick(project.uuid);
                        }}
                      >
                        Open
                      </Button>
                    </CardActions>
                  </Card>
                ))}
              </Box>
            </Paper>
          )}

          {isLoadingProjects && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <CircularProgress sx={{ color: 'white' }} />
            </Box>
          )}
        </Container>
      </Box>

      <HistoryModal 
        open={historyOpen} 
        onClose={() => setHistoryOpen(false)} 
      />
    </>
  );
};

export default Home; 