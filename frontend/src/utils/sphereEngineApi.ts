import api from './api';

export interface WorkspaceConfig {
  projectId?: string;
  projectType?: string;
  interviewUuid: string;
  files?: Array<{
    path: string;
    content: string;
  }>;
}

export interface WorkspaceResponse {
  id: string;
  project: {
    id: string;
    name: string;
  };
  state: {
    code: number;
    name: string;
  };
  workspace_url: string;
  workspace_token?: string;
}

export interface SupportedLanguage {
  id: number;
  name: string;
  extension: string;
}

export interface WorkspaceFile {
  path: string;
  content: string;
  size?: number;
  lastModified?: string;
}

export interface WorkspaceFileList {
  files: Array<{
    name: string;
    path: string;
    type: 'file' | 'directory';
    size?: number;
    lastModified?: string;
  }>;
  currentPath: string;
}

export interface FileOperationResponse {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

// Sphere Engine API methods
export const sphereEngineAPI = {
  // Create a new workspace
  createWorkspace: async (config: WorkspaceConfig) => {
    const response = await api.post('/sphere/workspaces', config);
    return response.data;
  },

  // Get or create workspace for interview session
  getOrCreateWorkspace: async (config: WorkspaceConfig) => {
    const response = await api.post('/sphere/workspaces/get-or-create', config);
    return response.data;
  },

  // List user workspaces
  listUserWorkspaces: async () => {
    const response = await api.get('/sphere/workspaces');
    return response.data;
  },

  // Check for existing workspace by interview UUID
  checkExistingWorkspace: async (interviewUuid: string) => {
    const response = await api.get(`/sphere/workspaces/check/${interviewUuid}`);
    return response.data;
  },

  // Get workspace status
  getWorkspaceStatus: async (workspaceId: string) => {
    const response = await api.get(`/sphere/workspaces/${workspaceId}`);
    return response.data;
  },

  // Stop a workspace
  stopWorkspace: async (workspaceId: string) => {
    const response = await api.post(`/sphere/workspaces/${workspaceId}/stop`);
    return response.data;
  },

  // Remove a workspace
  removeWorkspace: async (workspaceId: string) => {
    const response = await api.delete(`/sphere/workspaces/${workspaceId}`);
    return response.data;
  },

  // Create a project
  createProject: async (projectData: {
    name: string;
    type: string;
    description?: string;
    files?: Array<{ path: string; content: string }>;
  }) => {
    const response = await api.post('/sphere/projects', projectData);
    return response.data;
  },

  // Get supported programming languages
  getSupportedLanguages: async () => {
    const response = await api.get('/sphere/languages');
    return response.data;
  },

  // Upload generated code to workspace
  uploadCodeToWorkspace: async (workspaceId: string, projectStructure: any, interviewUuid: string) => {
    try {
      const files = extractFilesFromProjectStructure(projectStructure);

      // Create a new workspace with the extracted files
      return await sphereEngineAPI.createWorkspace({
        projectId: workspaceId,
        interviewUuid: interviewUuid,
        files: files
      });
    } catch (error) {
      console.error('Error uploading code to workspace:', error);
      throw error;
    }
  },

  // File management methods

  // Upload file to workspace
  uploadFileToWorkspace: async (workspaceId: string, filepath: string, content: string) => {
    const response = await api.post(`/sphere/workspaces/${workspaceId}/files`, {
      filepath,
      content
    });
    return response.data;
  },

  // Get file from workspace
  getFileFromWorkspace: async (workspaceId: string, filepath: string) => {
    const response = await api.get(`/sphere/workspaces/${workspaceId}/files/${encodeURIComponent(filepath)}`);
    return response.data;
  },

  // List files in workspace
  listWorkspaceFiles: async (workspaceId: string, path?: string) => {
    const params = path ? { path } : {};
    const response = await api.get(`/sphere/workspaces/${workspaceId}/files`, { params });
    return response.data;
  },

  // Modify existing file in workspace
  modifyFileInWorkspace: async (workspaceId: string, filepath: string, content: string) => {
    const response = await api.put(`/sphere/workspaces/${workspaceId}/files`, {
      filepath,
      content
    });
    return response.data;
  },

  // Create new file in workspace (alias for upload)
  createFileInWorkspace: async (workspaceId: string, filepath: string, content: string) => {
    return await sphereEngineAPI.uploadFileToWorkspace(workspaceId, filepath, content);
  }
};

// Helper function to extract files from project structure
const extractFilesFromProjectStructure = (structure: any, basePath: string = ''): Array<{ path: string; content: string }> => {
  const files: Array<{ path: string; content: string }> = [];

  if (!structure) return files;

  for (const [key, value] of Object.entries(structure)) {
    const fullPath = basePath ? `${basePath}/${key}` : key;

    if (value && typeof value === 'object') {
      const objValue = value as any;

      if (objValue.type === 'file') {
        files.push({
          path: fullPath,
          content: objValue.content || ''
        });
      } else if (objValue.type === 'folder' && objValue.children) {
        files.push(...extractFilesFromProjectStructure(objValue.children, fullPath));
      } else if (!objValue.type) {
        // Handle nested objects without explicit type
        files.push(...extractFilesFromProjectStructure(value, fullPath));
      }
    }
  }

  return files;
};

// Project templates for different types of applications
export const projectTemplates = {
  // React project template
  react: {
    name: 'React Application',
    description: 'A modern React application with TypeScript',
    files: [
      {
        path: 'package.json',
        content: JSON.stringify({
          name: 'react-app',
          version: '1.0.0',
          dependencies: {
            'react': '^18.0.0',
            'react-dom': '^18.0.0',
            'typescript': '^4.0.0'
          },
          scripts: {
            'start': 'react-scripts start',
            'build': 'react-scripts build',
            'test': 'react-scripts test'
          }
        }, null, 2)
      },
      {
        path: 'src/App.tsx',
        content: `import React from 'react';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>Hello, World!</h1>
        <p>Welcome to your new React application!</p>
      </header>
    </div>
  );
}

export default App;`
      },
      {
        path: 'src/index.tsx',
        content: `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);`
      },
      {
        path: 'public/index.html',
        content: `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>React App</title>
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>`
      }
    ]
  },

  // Node.js project template
  nodejs: {
    name: 'Node.js Application',
    description: 'A simple Node.js server application',
    files: [
      {
        path: 'package.json',
        content: JSON.stringify({
          name: 'nodejs-app',
          version: '1.0.0',
          main: 'index.js',
          scripts: {
            'start': 'node index.js',
            'dev': 'nodemon index.js'
          },
          dependencies: {
            'express': '^4.18.0'
          }
        }, null, 2)
      },
      {
        path: 'index.js',
        content: `const express = require('express');
const app = express();
const port = process.env.PORT || 3000;

app.use(express.json());

app.get('/', (req, res) => {
  res.json({ message: 'Hello, World!' });
});

app.listen(port, () => {
  console.log(\`Server running on port \${port}\`);
});`
      }
    ]
  },

  // Python project template
  python: {
    name: 'Python Application',
    description: 'A simple Python web application with Flask',
    files: [
      {
        path: 'requirements.txt',
        content: 'Flask==2.0.1\nrequests==2.25.1'
      },
      {
        path: 'app.py',
        content: `from flask import Flask, jsonify

app = Flask(__name__)

@app.route('/')
def hello_world():
    return jsonify({'message': 'Hello, World!'})

@app.route('/api/data')
def get_data():
    return jsonify({'data': [1, 2, 3, 4, 5]})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)`
      }
    ]
  }
}; 