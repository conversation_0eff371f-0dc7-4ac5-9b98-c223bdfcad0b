import { Schema, model, Document } from 'mongoose';

export interface IWorkspace extends Document {
  userId: string;
  interviewUuid: string;
  sphereEngineWorkspaceId: string;
  sphereEngineProjectId: string;
  createdAt: Date;
  updatedAt: Date;
}

const WorkspaceSchema = new Schema<IWorkspace>(
  {
    userId: {
      type: String,
      required: true,
      index: true
    },
    interviewUuid: {
      type: String,
      required: true,
      index: true
    },
    sphereEngineWorkspaceId: {
      type: String,
      required: true,
      unique: true
    },
    sphereEngineProjectId: {
      type: String,
      required: true
    }
  },
  {
    timestamps: true,
    collection: 'workspaces'
  }
);

// Compound index for efficient lookups by user + interview combination
WorkspaceSchema.index({ userId: 1, interviewUuid: 1 }, { unique: true });

// Index for Sphere Engine workspace ID lookups
WorkspaceSchema.index({ sphereEngineWorkspaceId: 1 });

export const Workspace = model<IWorkspace>('Workspace', WorkspaceSchema);
