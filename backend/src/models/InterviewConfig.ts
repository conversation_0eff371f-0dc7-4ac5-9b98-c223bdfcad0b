import { Schema, model, Document } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

// Interface for question-answer pairs within a section
interface QuestionAnswer {
  [questionName: string]: string;
}

// Interface for project data structure matching the schema
interface ProjectData {
  technical: {
    [sectionName: string]: QuestionAnswer[];
  };
  functional: {
    [sectionName: string]: QuestionAnswer[];
  };
}

export interface IInterviewConfig extends Document {
  uuid: string;
  user: {
    id?: string;
    email?: string;
    prompt?: string;
  };
  projectData: ProjectData;
  createdAt: Date;
  updatedAt: Date;
}

const InterviewConfigSchema = new Schema<IInterviewConfig>(
  {
    uuid: {
      type: String,
      required: true,
      unique: true,
      default: uuidv4
    },
    user: {
      id: {
        type: String,
        required: false
      },
      email: {
        type: String,
        required: false
      },
      prompt: {
        type: String,
        required: false,
        trim: true
      }
    },
    projectData: {
      technical: {
        type: Schema.Types.Mixed,
        required: true,
        default: {}
      },
      functional: {
        type: Schema.Types.Mixed,
        required: true,
        default: {}
      }
    }
  },
  {
    timestamps: true,
    collection: 'interview_configs'
  }
);

// Index for faster user history lookups
InterviewConfigSchema.index({ 'user.id': 1, updatedAt: -1 });

export const InterviewConfig = model<IInterviewConfig>('InterviewConfig', InterviewConfigSchema); 