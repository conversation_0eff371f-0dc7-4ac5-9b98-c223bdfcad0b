import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { connectDB } from './config/database';
import authRoutes from './routes/authRoutes';
import interviewRoutes from './routes/interviewRoutes';
import buildRoutes from './routes/buildRoutes';
import projectRoutes from './routes/projectRoutes';
import workspaceRoutes from './routes/workspaceRoutes';
import sphereEngineRoutes from './routes/sphereEngineRoutes';
import llmRoutes from './routes/llmRoutes';
import conversationRoutes from './routes/conversationRoutes';
import testRoutes from './routes/testRoutes';

// Load environment variables
dotenv.config();

// Initialize express app
const app = express();
const port = process.env.PORT || 8000;

// Connect to MongoDB
connectDB();

// Middleware
app.use(cors());
app.use(express.json());

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/interview', interviewRoutes);
app.use('/api/build', buildRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api/workspace', workspaceRoutes);
app.use('/api/sphere', sphereEngineRoutes);
app.use('/api/llm', llmRoutes);
app.use('/api/conversation', conversationRoutes);
app.use('/api/test', testRoutes);

// Basic health check endpoint
app.get('/api/health', (req: Request, res: Response) => {
  res.json({
    status: 'ok',
    mode: 'mock-testing-with-payload-logging',
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Something went wrong!' });
});

// Start server
app.listen(port, () => {
  console.log(`Server is running on port ${port} - DEBUG MODE`);
}); 