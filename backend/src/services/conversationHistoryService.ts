import { ConversationMessage, IConversationMessage, AnthropicMessage } from '../models/ConversationHistory';

export interface ConversationContext {
  messages: AnthropicMessage[];
  totalTokens: number;
  messageCount: number;
}

export interface SaveMessageRequest {
  interviewUuid: string;
  messageType: 'user' | 'assistant';
  content: string;
  inputTokens?: number;
  outputTokens?: number;
  totalTokens?: number;
  model?: string;
  parentMessageId?: string;
}

/**
 * Service for managing conversation history and context formatting
 */
export class ConversationHistoryService {
  
  /**
   * Get conversation history for an interview UUID
   */
  static async getConversationHistory(
    interviewUuid: string,
    limit?: number,
    before?: Date
  ): Promise<IConversationMessage[]> {
    try {
      const query: any = { interviewUuid };

      if (before) {
        query.timestamp = { $lt: before };
      }

      let queryBuilder = ConversationMessage.find(query)
        .sort({ timestamp: 1 }) // Chronological order
        .lean();

      if (limit) {
        queryBuilder = queryBuilder.limit(limit);
      }

      return await queryBuilder.exec();
    } catch (error) {
      console.error('Error retrieving conversation history:', error);
      throw new Error('Failed to retrieve conversation history');
    }
  }

  /**
   * Format conversation history for Anthropic API
   * Formats messages for API consumption
   */
  static formatForAnthropicAPI(messages: IConversationMessage[]): AnthropicMessage[] {
    return messages.map(msg => ({
      role: msg.messageType as 'user' | 'assistant',
      content: msg.content
    }));
  }

  /**
   * Get conversation context for API calls
   * Returns formatted messages and metadata
   */
  static async getConversationContext(
    interviewUuid: string,
    maxMessages?: number
  ): Promise<ConversationContext> {
    try {
      const messages = await this.getConversationHistory(interviewUuid, maxMessages);
      const formattedMessages = this.formatForAnthropicAPI(messages);
      
      const totalTokens = messages.reduce((sum, msg) => {
        return sum + (msg.totalTokens || 0);
      }, 0);

      return {
        messages: formattedMessages,
        totalTokens,
        messageCount: messages.length
      };
    } catch (error) {
      console.error('Error getting conversation context:', error);
      throw new Error('Failed to get conversation context');
    }
  }

  /**
   * Save a new message to conversation history
   */
  static async saveMessage(request: SaveMessageRequest): Promise<IConversationMessage> {
    try {
      // Get the next message order for this interview
      const lastMessage = await ConversationMessage.findOne({
        interviewUuid: request.interviewUuid
      }).sort({ messageOrder: -1 });

      const nextOrder = lastMessage ? lastMessage.messageOrder + 1 : 1;

      const message = new ConversationMessage({
        interviewUuid: request.interviewUuid,
        messageType: request.messageType,
        content: request.content,
        timestamp: new Date(),
        messageOrder: nextOrder,
        inputTokens: request.inputTokens,
        outputTokens: request.outputTokens,
        totalTokens: request.totalTokens,
        llmModel: request.model,
        parentMessageId: request.parentMessageId
      });

      return await message.save();
    } catch (error) {
      console.error('Error saving message:', error);
      throw new Error('Failed to save message');
    }
  }

  /**
   * Save both user and assistant messages in a single transaction
   */
  static async saveMessagePair(
    interviewUuid: string,
    userMessage: string,
    assistantMessage: string,
    metadata?: {
      inputTokens?: number;
      outputTokens?: number;
      totalTokens?: number;
      model?: string;
    }
  ): Promise<{
    userMessage: IConversationMessage;
    assistantMessage: IConversationMessage;
  }> {
    try {
      // Save user message first
      const savedUserMessage = await this.saveMessage({
        interviewUuid,
        messageType: 'user',
        content: userMessage
      });

      // Save assistant message with token metadata
      const savedAssistantMessage = await this.saveMessage({
        interviewUuid,
        messageType: 'assistant',
        content: assistantMessage,
        inputTokens: metadata?.inputTokens,
        outputTokens: metadata?.outputTokens,
        totalTokens: metadata?.totalTokens,
        model: metadata?.model,
        parentMessageId: savedUserMessage.messageId
      });

      return {
        userMessage: savedUserMessage,
        assistantMessage: savedAssistantMessage
      };
    } catch (error) {
      console.error('Error saving message pair:', error);
      throw new Error('Failed to save message pair');
    }
  }

  /**
   * Get conversation statistics
   */
  static async getConversationStats(interviewUuid: string): Promise<{
    totalMessages: number;
    userMessages: number;
    assistantMessages: number;
    totalTokens: number;
    lastMessageAt?: Date;
  }> {
    try {
      const [totalMessages, userMessages, assistantMessages, allMessages] = await Promise.all([
        ConversationMessage.countDocuments({ interviewUuid }),
        ConversationMessage.countDocuments({ interviewUuid, messageType: 'user' }),
        ConversationMessage.countDocuments({ interviewUuid, messageType: 'assistant' }),
        this.getConversationHistory(interviewUuid)
      ]);

      const totalTokens = allMessages.reduce((sum: number, msg: IConversationMessage) => sum + (msg.totalTokens || 0), 0);
      const lastMessage = allMessages[allMessages.length - 1];

      return {
        totalMessages,
        userMessages,
        assistantMessages,
        totalTokens,
        lastMessageAt: lastMessage?.timestamp
      };
    } catch (error) {
      console.error('Error getting conversation stats:', error);
      throw new Error('Failed to get conversation stats');
    }
  }

  /**
   * Get complete conversation context for LLM API calls
   * Returns all messages in chronological order with current user message appended
   */
  static async getCompleteConversationContext(
    interviewUuid: string,
    currentUserMessage: string
  ): Promise<AnthropicMessage[]> {
    try {
      // Get all previous messages for this interview
      const previousMessages = await ConversationMessage.find({
        interviewUuid
      }).sort({ messageOrder: 1 });

      // Format previous messages for API
      const formattedPreviousMessages = this.formatForAnthropicAPI(previousMessages);

      // Add current user message
      const currentMessage: AnthropicMessage = {
        role: 'user',
        content: currentUserMessage
      };

      // Return complete conversation history
      return [...formattedPreviousMessages, currentMessage];
    } catch (error) {
      console.error('Error getting complete conversation context:', error);
      throw new Error('Failed to get conversation context');
    }
  }

  /**
   * Clear conversation history for an interview
   */
  static async clearConversation(interviewUuid: string): Promise<boolean> {
    try {
      const result = await ConversationMessage.deleteMany({ interviewUuid });
      return result.deletedCount > 0;
    } catch (error) {
      console.error('Error clearing conversation:', error);
      throw new Error('Failed to clear conversation');
    }
  }

  /**
   * Get the last N messages for context window management
   */
  static async getRecentMessages(
    interviewUuid: string,
    count: number
  ): Promise<IConversationMessage[]> {
    try {
      const allMessages = await this.getConversationHistory(interviewUuid);
      return allMessages.slice(-count); // Get last N messages
    } catch (error) {
      console.error('Error getting recent messages:', error);
      throw new Error('Failed to get recent messages');
    }
  }

  /**
   * Validate conversation context for API limits
   * Ensures the conversation doesn't exceed token limits
   */
  static validateContextSize(
    messages: AnthropicMessage[],
    maxTokens: number = 60000 // Leave room for response
  ): {
    isValid: boolean;
    estimatedTokens: number;
    suggestedTrimCount?: number;
  } {
    // Rough estimation: 1 token ≈ 4 characters
    const estimatedTokens = messages.reduce((sum, msg) => {
      return sum + Math.ceil(msg.content.length / 4);
    }, 0);

    if (estimatedTokens <= maxTokens) {
      return { isValid: true, estimatedTokens };
    }

    // Calculate how many messages to trim
    let currentTokens = estimatedTokens;
    let trimCount = 0;
    
    for (let i = 0; i < messages.length && currentTokens > maxTokens; i++) {
      currentTokens -= Math.ceil(messages[i].content.length / 4);
      trimCount++;
    }

    return {
      isValid: false,
      estimatedTokens,
      suggestedTrimCount: trimCount
    };
  }

  /**
   * Trim conversation context to fit within token limits
   */
  static trimContextForAPI(
    messages: AnthropicMessage[],
    maxTokens: number = 60000
  ): AnthropicMessage[] {
    const validation = this.validateContextSize(messages, maxTokens);
    
    if (validation.isValid) {
      return messages;
    }

    if (validation.suggestedTrimCount) {
      // Keep the most recent messages
      return messages.slice(validation.suggestedTrimCount);
    }

    return messages;
  }
}
