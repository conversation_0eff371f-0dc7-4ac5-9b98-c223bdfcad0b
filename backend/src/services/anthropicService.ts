import axios, { AxiosResponse } from 'axios';

export interface AnthropicMessage {
  role: 'user' | 'assistant';
  content: string;
}

export interface AnthropicRequest {
  model: string;
  max_tokens: number;
  messages: AnthropicMessage[];
}

export interface AnthropicResponseContent {
  text: string;
  type: 'text';
}

export interface AnthropicResponse {
  content: AnthropicResponseContent[];
  id: string;
  model: string;
  role: 'assistant';
  stop_reason: string;
  stop_sequence: string | null;
  type: 'message';
  usage: {
    input_tokens: number;
    output_tokens: number;
  };
}

export interface LLMMetadata {
  prompt: string;
  model: string;
  tokens?: number;
  timestamp: Date;
  inputTokens?: number;
  outputTokens?: number;
}

/**
 * Service for interacting with Anthropic's Claude API
 */
export class AnthropicService {
  private static readonly API_URL = 'https://api.anthropic.com/v1/messages';
  private static readonly API_VERSION = '2023-06-01';
  private static readonly MODEL = 'claude-sonnet-4-20250514';
  private static readonly MAX_TOKENS = 64000;

  /**
   * Validate that the API key is configured
   */
  private static validateApiKey(): string {
    const apiKey = process.env.ANTHROPIC_API_KEY;
    if (!apiKey || apiKey === 'your_api_key') {
      throw new Error('ANTHROPIC_API_KEY is not properly configured in environment variables');
    }
    return apiKey;
  }

  /**
   * Format the context with the required prefix
   */
  private static formatContext(prompt: string, projectData: any): string {
    const contextData = {
      objective: prompt,
      requirement: projectData
    };
    
    const contextString = JSON.stringify(contextData, null, 2);
    return `Generate code for context: ${contextString}
    
    The output format of code part should be: ### <file_name>\n\`\`\`<language>\n<code>\`\`\`\n\n
    
    Create backend folder for backend code and frontend folder for frontend code.

    Please build frontend (if needed) on top of React + Vite. 
    Please use the following server setting in vite.config.js:
    server: {
      host: "::",
      port: 8080,
      strictPort: true,
      allowedHosts: ['.containers.sphere-engine.com'],
    }
    Finally make sure we can just use "npm install" and then "npm run dev" to run the frontend.
    `;
  }

  /**
   * Generate LLM response using Anthropic API or mock data (only when API not configured)
   */
  static async generateResponse(
    prompt: string,
    projectData: any,
    userData?: any
  ): Promise<{
    content: string;
    metadata: LLMMetadata;
  }> {
    try {
      // Check if we should bypass API key requirement (for testing)
      const bypassApiKey = process.env.BYPASS_API_KEY === 'true';

      if (bypassApiKey) {
        console.log('🧪 MOCK MODE: Using doc/llm_response.json for project build response');
        const formattedContext = this.formatContext(prompt, projectData);
        return this.generateFallbackResponse(formattedContext, 'Mock mode enabled for testing');
      }

      // Validate API key for production
      const apiKey = this.validateApiKey();

      // Format the context for Anthropic API
      const formattedContext = this.formatContext(prompt, projectData);

      // Prepare the request payload
      const requestPayload: AnthropicRequest = {
        model: this.MODEL,
        max_tokens: this.MAX_TOKENS,
        messages: [
          {
            role: 'user',
            content: formattedContext
          }
        ]
      };

      console.log('🚀 Making Anthropic API request with payload:', {
        model: requestPayload.model,
        max_tokens: requestPayload.max_tokens,
        messageLength: formattedContext.length
      });

      console.log('📋 FULL INITIAL BUILD PAYLOAD CONTENT:');
      console.log(JSON.stringify(requestPayload, null, 2));

      // Make the API request
      const response: AxiosResponse<AnthropicResponse> = await axios.post(
        this.API_URL,
        requestPayload,
        {
          headers: {
            'x-api-key': apiKey,
            'anthropic-version': this.API_VERSION,
            'content-type': 'application/json'
          },
          timeout: 300000 // 5 minutes timeout
        }
      );

      console.log('✅ Anthropic API response received:', {
        id: response.data.id,
        model: response.data.model,
        inputTokens: response.data.usage.input_tokens,
        outputTokens: response.data.usage.output_tokens
      });

      // Extract the text content from the response
      const content = response.data.content
        .filter(item => item.type === 'text')
        .map(item => item.text)
        .join('\n');

      if (!content) {
        throw new Error('No text content found in Anthropic API response');
      }

      // Prepare metadata
      const metadata: LLMMetadata = {
        prompt: formattedContext,
        model: response.data.model,
        tokens: response.data.usage.input_tokens + response.data.usage.output_tokens,
        inputTokens: response.data.usage.input_tokens,
        outputTokens: response.data.usage.output_tokens,
        timestamp: new Date()
      };

      return {
        content,
        metadata
      };

    } catch (error: any) {
      console.error('❌ Anthropic API error:', error);

      // Handle specific error types with proper error messages
      // DO NOT use mock data fallback for real API errors to avoid user confusion
      if (error.response) {
        const status = error.response.status;
        const errorData = error.response.data;

        if (status === 401) {
          throw new Error('Invalid Anthropic API key. Please check your ANTHROPIC_API_KEY environment variable.');
        } else if (status === 429) {
          throw new Error('Anthropic API rate limit exceeded. Please try again later.');
        } else if (status === 400) {
          throw new Error(`Invalid request to Anthropic API: ${errorData?.error?.message || 'Bad request'}`);
        } else if (status === 403) {
          throw new Error('Access forbidden. Please check your Anthropic API key permissions.');
        } else if (status >= 500) {
          throw new Error(`Anthropic API server error (${status}). Please try again later.`);
        } else {
          throw new Error(`Anthropic API error (${status}): ${errorData?.error?.message || 'Unknown error'}`);
        }
      } else if (error.code === 'ECONNABORTED') {
        throw new Error('Anthropic API request timed out. Please try again.');
      } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        throw new Error('Unable to connect to Anthropic API. Please check your internet connection.');
      } else if (error.code === 'ENOTFOUND') {
        throw new Error('Network error: Unable to reach Anthropic API. Please check your internet connection.');
      } else {
        throw new Error(`Anthropic API service error: ${error.message}`);
      }
    }
  }

  /**
   * Generate fallback response using mock data when Anthropic API fails
   */
  private static async generateFallbackResponse(
    prompt: string,
    errorMessage: string
  ): Promise<{
    content: string;
    metadata: LLMMetadata;
  }> {
    try {
      const path = require('path');
      const fs = require('fs');

      // Try to read the mock response content from doc/llm_response.json
      const mockResponsePath = path.join(process.cwd(), '..', 'doc', 'llm_response.json');
      const mockResponseContent = await fs.promises.readFile(mockResponsePath, 'utf-8');
      const mockResponse = JSON.parse(mockResponseContent);

      console.log('✅ Fallback: Using mock data from doc/llm_response.json');

      // Extract the text content from the mock response
      const content = mockResponse.content
        .filter((item: any) => item.type === 'text')
        .map((item: any) => item.text)
        .join('\n');

      // Prepare fallback metadata using mock response data
      const metadata: LLMMetadata = {
        prompt: prompt,
        model: mockResponse.model || 'claude-sonnet-4-mock',
        tokens: (mockResponse.usage?.input_tokens || 0) + (mockResponse.usage?.output_tokens || 0),
        inputTokens: mockResponse.usage?.input_tokens || 0,
        outputTokens: mockResponse.usage?.output_tokens || 0,
        timestamp: new Date()
      };

      return {
        content,
        metadata
      };

    } catch (fallbackError: any) {
      console.error('❌ Fallback also failed:', fallbackError);
      throw new Error(`Anthropic API failed (${errorMessage}) and fallback mock data is not available: ${fallbackError.message}`);
    }
  }

  /**
   * Check if the API key is configured (for health checks)
   */
  static isConfigured(): boolean {
    try {
      this.validateApiKey();
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get API configuration status
   */
  static getConfigStatus(): {
    configured: boolean;
    model: string;
    maxTokens: number;
  } {
    return {
      configured: this.isConfigured(),
      model: this.MODEL,
      maxTokens: this.MAX_TOKENS
    };
  }
}
