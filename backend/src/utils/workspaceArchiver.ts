import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { exec } from 'child_process';
import { randomUUID } from 'crypto';

const execAsync = promisify(exec);

interface ExtractedStructure {
  description?: string;
  codeBlocks: Array<{
    language: string;
    filename?: string;
    content: string;
  }>;
  projectStructure?: any;
  deploymentInstructions?: string;
  additionalSections?: { [key: string]: string };
}

interface ArchiveResult {
  success: boolean;
  archivePath?: string;
  structureJsonPath?: string;
  error?: string;
}

export class WorkspaceArchiver {

  /**
   * Clean up old archive files (older than 1 hour)
   */
  static async cleanupOldArchives(): Promise<void> {
    try {
      const workspaceDir = path.join(process.cwd(), 'workspace');
      const files = await fs.promises.readdir(workspaceDir);
      const oneHourAgo = Date.now() - (60 * 60 * 1000); // 1 hour in milliseconds

      for (const file of files) {
        if (file.startsWith('source_') && file.endsWith('.tar.gz')) {
          const filePath = path.join(workspaceDir, file);
          try {
            const stats = await fs.promises.stat(filePath);
            if (stats.mtime.getTime() < oneHourAgo) {
              await fs.promises.unlink(filePath);
              console.log(`🧹 Cleaned up old archive: ${file}`);
            }
          } catch (error) {
            console.warn(`⚠️ Failed to cleanup old archive ${file}:`, error);
          }
        }
      }
    } catch (error) {
      console.warn('⚠️ Failed to cleanup old archives:', error);
    }
  }

  /**
   * Create workspace archive with only extracted_structure.json
   */
  static async createWorkspaceArchive(
    extractedContent: ExtractedStructure,
    workspaceId: string,
    interviewUuid: string
  ): Promise<ArchiveResult> {
    // Clean up old archives before creating new one
    await this.cleanupOldArchives();

    // Generate unique temporary directory to prevent race conditions
    const uniqueId = randomUUID();
    const tempDir = path.join(process.cwd(), `temp_archive_${uniqueId}`);
    const structureDir = path.join(tempDir, 'structure');

    try {
      console.log(`📁 Creating unique temp directory: ${tempDir}`);

      // Ensure directories exist
      await fs.promises.mkdir(structureDir, { recursive: true });

      // Save only the pure projectStructure content (no wrapper fields)
      const structureJsonPath = path.join(structureDir, 'extracted_structure.json');
      const projectStructure = extractedContent.projectStructure || {};

      await fs.promises.writeFile(
        structureJsonPath,
        JSON.stringify(projectStructure, null, 2),
        'utf-8'
      );

      // Create unique archive path to prevent conflicts
      const archivePath = path.join(process.cwd(), 'workspace', `source_${uniqueId}.tar.gz`);
      await fs.promises.mkdir(path.dirname(archivePath), { recursive: true });

      console.log(`📦 Creating archive: ${archivePath}`);

      // Use GNU tar with --transform (exactly as you did successfully)
      const command = `cd "${tempDir}" && gtar -czf "${archivePath}" --transform='s,^,workspace/,' structure/`;
      await execAsync(command);

      console.log(`✅ Archive created successfully: ${archivePath}`);

      // Clean up temp directory
      await fs.promises.rm(tempDir, { recursive: true, force: true });
      console.log(`🧹 Cleaned up temp directory: ${tempDir}`);

      return {
        success: true,
        archivePath,
        structureJsonPath
      };

    } catch (error: any) {
      console.error('Error creating workspace archive:', error);

      // Ensure cleanup even on error
      try {
        await fs.promises.rm(tempDir, { recursive: true, force: true });
        console.log(`🧹 Emergency cleanup completed for: ${tempDir}`);
      } catch (cleanupError) {
        console.warn(`⚠️ Failed to cleanup temp directory ${tempDir}:`, cleanupError);
      }

      return {
        success: false,
        error: error.message
      };
    }
  }
}
