import { Request, Response } from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import fs from 'fs/promises';
import path from 'path';

interface WorkspaceFile {
  path: string;
  content: string;
  language: string;
  isDirectory: boolean;
  lastModified: Date;
}

interface WorkspaceStructure {
  [key: string]: any;
}

// Base workspace directory - you can make this configurable
const BASE_WORKSPACE_DIR = '/Users/<USER>/Desktop/Mergen-AI/test_coding_platform';

export class WorkspaceManager {
  static async createWorkspaceDir(workspaceName: string): Promise<string> {
    const workspaceDir = path.join(BASE_WORKSPACE_DIR, workspaceName);
    await fs.mkdir(workspaceDir, { recursive: true });
    return workspaceDir;
  }

  static async ensureWorkspaceExists(workspacePath: string): Promise<boolean> {
    try {
      const stats = await fs.stat(workspacePath);
      return stats.isDirectory();
    } catch {
      return false;
    }
  }

  static async readWorkspaceStructure(workspacePath: string, basePath: string = ''): Promise<WorkspaceStructure> {
    const structure: WorkspaceStructure = {};

    try {
      const items = await fs.readdir(workspacePath, { withFileTypes: true });

      for (const item of items) {
        // Skip hidden files and node_modules
        if (item.name.startsWith('.') || item.name === 'node_modules') {
          continue;
        }

        const fullPath = path.join(workspacePath, item.name);
        const relativePath = basePath ? `${basePath}/${item.name}` : item.name;

        if (item.isDirectory()) {
          const children = await this.readWorkspaceStructure(fullPath, relativePath);
          structure[item.name] = {
            type: 'folder',
            children: children
          };
        } else {
          try {
            const content = await fs.readFile(fullPath, 'utf-8');
            const stats = await fs.stat(fullPath);
            
            structure[item.name] = {
              type: 'file',
              content: content,
              language: this.getLanguageFromFilename(item.name),
              lastModified: stats.mtime
            };
          } catch (error) {
            console.warn(`Could not read file ${fullPath}:`, error);
            // Still include the file but with empty content
            structure[item.name] = {
              type: 'file',
              content: '',
              language: this.getLanguageFromFilename(item.name),
              lastModified: new Date()
            };
          }
        }
      }
    } catch (error) {
      console.error(`Error reading workspace ${workspacePath}:`, error);
    }

    return structure;
  }

  static async writeFile(workspacePath: string, filePath: string, content: string): Promise<void> {
    const fullPath = path.join(workspacePath, filePath);
    const dirPath = path.dirname(fullPath);
    
    // Ensure directory exists
    await fs.mkdir(dirPath, { recursive: true });
    
    // Write file
    await fs.writeFile(fullPath, content, 'utf-8');
  }

  static async deleteFile(workspacePath: string, filePath: string): Promise<void> {
    const fullPath = path.join(workspacePath, filePath);
    await fs.unlink(fullPath);
  }

  static async readFile(workspacePath: string, filePath: string): Promise<string> {
    const fullPath = path.join(workspacePath, filePath);
    return await fs.readFile(fullPath, 'utf-8');
  }

  static getLanguageFromFilename(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase();
    const langMap: { [key: string]: string } = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'json': 'json',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'yml': 'yaml',
      'yaml': 'yaml',
      'xml': 'xml',
      'md': 'markdown',
      'txt': 'text',
      'env': 'bash',
      'sh': 'bash',
      'dockerfile': 'dockerfile'
    };
    return langMap[ext || ''] || 'text';
  }
}

// Get workspace structure for a specific workspace
export const getWorkspaceStructure = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const workspacePath = path.join(BASE_WORKSPACE_DIR, workspaceName);

    // Check if workspace exists
    const exists = await WorkspaceManager.ensureWorkspaceExists(workspacePath);
    if (!exists) {
      return res.status(404).json({
        success: false,
        message: 'Workspace not found'
      });
    }

    const structure = await WorkspaceManager.readWorkspaceStructure(workspacePath);

    res.json({
      success: true,
      data: {
        workspacePath,
        structure
      },
      message: 'Workspace structure retrieved successfully'
    });

  } catch (error: any) {
    console.error('Error getting workspace structure:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving workspace structure',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Read a specific file from workspace
export const readWorkspaceFile = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const { filePath } = req.body;
    
    const workspacePath = path.join(BASE_WORKSPACE_DIR, workspaceName);
    const content = await WorkspaceManager.readFile(workspacePath, filePath);

    res.json({
      success: true,
      data: {
        path: filePath,
        content,
        language: WorkspaceManager.getLanguageFromFilename(path.basename(filePath))
      },
      message: 'File read successfully'
    });

  } catch (error: any) {
    console.error('Error reading workspace file:', error);
    res.status(500).json({
      success: false,
      message: 'Error reading file',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Write a file to workspace
export const writeWorkspaceFile = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const { filePath, content } = req.body;
    
    const workspacePath = path.join(BASE_WORKSPACE_DIR, workspaceName);
    await WorkspaceManager.writeFile(workspacePath, filePath, content);

    res.json({
      success: true,
      data: {
        path: filePath,
        content,
        language: WorkspaceManager.getLanguageFromFilename(path.basename(filePath))
      },
      message: 'File saved successfully'
    });

  } catch (error: any) {
    console.error('Error writing workspace file:', error);
    res.status(500).json({
      success: false,
      message: 'Error saving file',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Delete a file from workspace
export const deleteWorkspaceFile = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const { filePath } = req.body;
    
    const workspacePath = path.join(BASE_WORKSPACE_DIR, workspaceName);
    await WorkspaceManager.deleteFile(workspacePath, filePath);

    res.json({
      success: true,
      message: 'File deleted successfully'
    });

  } catch (error: any) {
    console.error('Error deleting workspace file:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting file',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Create a new workspace
export const createWorkspace = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.body;
    
    const workspacePath = await WorkspaceManager.createWorkspaceDir(workspaceName);

    res.json({
      success: true,
      data: {
        workspaceName,
        workspacePath
      },
      message: 'Workspace created successfully'
    });

  } catch (error: any) {
    console.error('Error creating workspace:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating workspace',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// List all available workspaces
export const listWorkspaces = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const items = await fs.readdir(BASE_WORKSPACE_DIR, { withFileTypes: true });
    const workspaces = items
      .filter(item => item.isDirectory())
      .map(item => item.name);

    res.json({
      success: true,
      data: workspaces,
      message: 'Workspaces retrieved successfully'
    });

  } catch (error: any) {
    console.error('Error listing workspaces:', error);
    res.status(500).json({
      success: false,
      message: 'Error listing workspaces',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}; 