import { Request, Response } from 'express';
import { InterviewConfig } from '../models/InterviewConfig';
import { CodeExtractor } from '../utils/codeExtractor';
import { AuthenticatedRequest } from '../middleware/auth';
import { BuildService } from '../services/buildService';
import { AnthropicService } from '../services/anthropicService';
import { BuildResult } from '../models/BuildResult';
import fs from 'fs';
import path from 'path';
import { WorkspaceArchiver } from '../utils/workspaceArchiver';
import { Workspace, IWorkspace } from '../models/Workspace';
import { ConversationHistoryService } from '../services/conversationHistoryService';
import FormData from 'form-data';
import fetch from 'node-fetch';


interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

/**
 * Create initial chat history from BuildResult extracted content
 */
function createInitialChatHistory(buildResult: any): ChatMessage[] {
  const messages: ChatMessage[] = [];
  let messageId = 1;

  // 1. Initial welcome message
  messages.push({
    id: messageId.toString(),
    type: 'ai',
    content: '🚀 Starting project generation...',
    timestamp: new Date()
  });
  messageId++;

  // 2. Description message (if available)
  if (buildResult.description) {
    messages.push({
      id: messageId.toString(),
      type: 'ai',
      content: `📋 **Project Overview**\n\n${buildResult.description}`,
      timestamp: new Date()
    });
    messageId++;
  }

  // 3. Code generation message
  if (buildResult.codeBlocks && buildResult.codeBlocks.length > 0) {
    messages.push({
      id: messageId.toString(),
      type: 'ai',
      content: `💻 **Code Generation Complete**\n\nI've generated ${buildResult.codeBlocks.length} code files for your project. The code structure is ready for review.`,
      timestamp: new Date()
    });
    messageId++;
  }

  // 4. Deployment instructions (if available)
  if (buildResult.deploymentInstructions) {
    messages.push({
      id: messageId.toString(),
      type: 'ai',
      content: `🚀 **Deployment Instructions**\n\n${buildResult.deploymentInstructions}`,
      timestamp: new Date()
    });
    messageId++;
  }

  // 5. Additional sections (if available)
  if (buildResult.additionalSections && buildResult.additionalSections.length > 0) {
    buildResult.additionalSections.forEach((section: any) => {
      messages.push({
        id: messageId.toString(),
        type: 'ai',
        content: `📝 **${section.title}**\n\n${section.content}`,
        timestamp: new Date()
      });
      messageId++;
    });
  }

  // 6. Final completion message
  messages.push({
    id: messageId.toString(),
    type: 'ai',
    content: '✅ Project generation complete! Your code is ready for review and deployment.',
    timestamp: new Date()
  });

  console.log('✅ Created', messages.length, 'initial chat messages from BuildResult');
  return messages;
}

interface GenerateCodeRequest {
  interviewUuid?: string;
  prompt?: string;
  projectData?: any;
  userData?: any;
}

interface ChatRequest {
  interviewUuid: string;
  message: string;
  conversationHistory?: ChatMessage[];
}

/**
 * Create a concise summary of the LLM response for chatbox display
 */
function createChatSummary(fullResponse: string, userMessage: string): string {
  try {
    // Extract key information from the response
    const lines = fullResponse.split('\n').filter(line => line.trim());

    // Look for file modifications/creations
    const fileMatches = fullResponse.match(/### ([^\n]+)/g) || [];
    const fileCount = fileMatches.length;

    // Look for key action words
    const hasCreated = /creat(e|ed|ing)/i.test(fullResponse);
    const hasModified = /modif(y|ied|ying)|updat(e|ed|ing)|chang(e|ed|ing)/i.test(fullResponse);
    const hasFixed = /fix(ed|ing)?|resolv(e|ed|ing)|correct(ed|ing)?/i.test(fullResponse);
    const hasAdded = /add(ed|ing)?|implement(ed|ing)?/i.test(fullResponse);

    // Create a concise summary based on detected actions
    let summary = "✅ I've processed your request";

    if (fileCount > 0) {
      summary += ` and updated ${fileCount} file${fileCount > 1 ? 's' : ''}`;
    }

    const actions = [];
    if (hasCreated) actions.push("created new components");
    if (hasModified) actions.push("modified existing code");
    if (hasFixed) actions.push("fixed issues");
    if (hasAdded) actions.push("added new features");

    if (actions.length > 0) {
      summary += `. I've ${actions.join(', ')}.`;
    } else {
      summary += ".";
    }

    // Add specific file mentions if reasonable length
    if (fileCount > 0 && fileCount <= 3) {
      const fileNames = fileMatches.map(match =>
        match.replace('### ', '').split('/').pop() // Get just filename
      ).join(', ');
      summary += ` Files updated: ${fileNames}.`;
    }

    // Add a helpful closing
    summary += " The changes have been applied to your workspace.";

    return summary;

  } catch (error) {
    console.error('❌ Error creating chat summary:', error);
    // Fallback to a generic but helpful message
    return "✅ I've processed your request and made the necessary changes to your codebase. The updates have been applied to your workspace.";
  }
}

interface SphereEngineConfig {
  customerId: string;
  accessToken: string;
  apiEndpoint: string;
}



// Helper function to create workspace record in database
const createWorkspaceRecord = async (
  userId: string,
  interviewUuid: string,
  sphereEngineWorkspaceId: string,
  sphereEngineProjectId: string
): Promise<IWorkspace> => {
  try {
    if (!userId || !interviewUuid || !sphereEngineWorkspaceId || !sphereEngineProjectId) {
      throw new Error('Missing required parameters for workspace record creation');
    }

    const workspace = new Workspace({
      userId,
      interviewUuid,
      sphereEngineWorkspaceId,
      sphereEngineProjectId
    });

    const savedWorkspace = await workspace.save();
    console.log(`💾 Created workspace record for user ${userId}, interview ${interviewUuid}:`, savedWorkspace.sphereEngineWorkspaceId);
    return savedWorkspace;
  } catch (error) {
    console.error('❌ Error creating workspace record:', error);
    throw error;
  }
};

// Import workspace functions from sphereEngineController
import { findExistingWorkspace, checkWorkspaceState, getSphereEngineConfig } from './sphereEngineController';

// Function to get or create workspace with archive (checks existing first)
const getOrCreateWorkspaceWithArchive = async (
  archivePath: string,
  interviewUuid: string,
  userId: string
): Promise<any> => {
  try {
    console.log('🔍 Checking for existing workspace before creating with archive...');

    // Check if workspace already exists for this user + interview combination
    const existingWorkspace = await findExistingWorkspace(userId, interviewUuid);

    if (existingWorkspace) {
      console.log('✅ Found existing workspace, checking state...');

      const config = getSphereEngineConfig();
      const stateCheck = await checkWorkspaceState(existingWorkspace.sphereEngineWorkspaceId, config);

      if (stateCheck.isRunning) {
        console.log('✅ Existing workspace is running, using it instead of creating new one');

        // Update the record timestamp
        await existingWorkspace.save();

        return {
          success: true,
          workspace: {
            id: existingWorkspace.sphereEngineWorkspaceId,
            projectId: existingWorkspace.sphereEngineProjectId,
            isExisting: true
          },
          message: `Using existing workspace (state: ${stateCheck.stateName})`
        };
      } else {
        console.log('❌ Existing workspace is not running, will create new one');
        // Delete the non-running workspace record
        await Workspace.deleteOne({ _id: existingWorkspace._id });
      }
    }

    // No existing workspace or existing one is not running - create new one
    console.log('🆕 Creating new workspace with archive...');
    return await createSphereEngineWorkspaceWithArchive(archivePath, interviewUuid, userId);

  } catch (error: any) {
    console.error('❌ Error in getOrCreateWorkspaceWithArchive:', error);
    return {
      success: false,
      error: error.message,
      skipWorkspace: true
    };
  }
};

// Helper function to wait for file to be ready with retries
const waitForFileReady = async (filePath: string, maxRetries: number = 5, delayMs: number = 1000): Promise<boolean> => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      // Check if file exists and is readable
      await fs.promises.access(filePath, fs.constants.R_OK);

      // Additional check: ensure file has content (not empty)
      const stats = await fs.promises.stat(filePath);
      if (stats.size > 0) {
        console.log(`✅ File ready after ${i + 1} attempts: ${filePath} (${stats.size} bytes)`);
        return true;
      }

      console.log(`⏳ File exists but empty, retrying... (${i + 1}/${maxRetries})`);
    } catch (error) {
      console.log(`⏳ File not ready, retrying... (${i + 1}/${maxRetries}): ${error}`);
    }

    if (i < maxRetries - 1) {
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
  }

  return false;
};

// Function to create Sphere Engine workspace with archive
const createSphereEngineWorkspaceWithArchive = async (
  archivePath: string,
  interviewUuid: string,
  userId: string
): Promise<any> => {
  try {
    const config = getSphereEngineConfig();

    console.log('🔧 Sphere Engine config:', {
      hasAccessToken: !!config.accessToken,
      hasApiEndpoint: !!config.apiEndpoint,
      customerId: config.customerId,
      apiEndpoint: config.apiEndpoint
    });

    if (!config.accessToken || !config.apiEndpoint) {
      console.error('❌ Sphere Engine configuration missing');
      return {
        success: false,
        error: 'Sphere Engine configuration missing',
        skipWorkspace: true
      };
    }

    // Wait for archive file to be ready with retries
    const fileReady = await waitForFileReady(archivePath);
    if (!fileReady) {
      console.error('❌ Archive file not ready after retries:', archivePath);
      return {
        success: false,
        error: 'Archive file not ready after retries',
        skipWorkspace: true
      };
    }

    console.log('📦 Archive file confirmed ready:', archivePath);

    // Create form data for the API request (matching the exact curl format)
    const formData = new FormData();
    formData.append('project_id', '025a0cfd469e4dc49b5bfbccc29539c4');

    // Read the archive file and append it exactly like curl does with @filename
    const archiveStream = fs.createReadStream(archivePath);
    formData.append('files', archiveStream, {
      filename: 'source.tar.gz',
      contentType: 'application/gzip'
    });

    console.log('📋 FormData fields:', {
      project_id: '025a0cfd469e4dc49b5bfbccc29539c4',
      files: `File stream: ${archivePath}`
    });

    // Make the API request (exactly as specified in the curl command)
    const apiUrl = `https://${config.apiEndpoint}/api/v1/workspaces?access_token=${config.accessToken}`;
    console.log('🌐 Making API request to:', apiUrl);

    // Make the API request using node-fetch with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        body: formData,
        headers: {
          ...formData.getHeaders()
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      console.log('📡 API response status:', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Sphere Engine API error:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });

      // Return success but with warning for non-critical errors
      return {
        success: false,
        error: `Sphere Engine API error: ${response.status} ${response.statusText}`,
        details: errorText,
        skipWorkspace: true
      };
    }

    const data = await response.json() as { workspace: any };
    console.log('✅ Sphere Engine workspace created successfully:', data);

    // Save workspace to database with user + interview scoping
    if (data.workspace?.id && userId && interviewUuid) {
      try {
        await createWorkspaceRecord(
          userId,
          interviewUuid,
          data.workspace.id,
          '025a0cfd469e4dc49b5bfbccc29539c4' // project_id used for workspace creation
        );
        console.log('💾 Workspace saved to database successfully');
      } catch (error) {
        console.error('❌ Error saving workspace to database:', error);
        // Continue anyway since workspace was created successfully
      }
    } else {
      console.warn('⚠️ Missing data for database save:', {
        hasWorkspaceId: !!data.workspace?.id,
        hasUserId: !!userId,
        hasInterviewUuid: !!interviewUuid
      });
    }

      return {
        success: true,
        workspace: data.workspace,
        message: 'Workspace created with archive successfully'
      };

    } catch (fetchError: any) {
      clearTimeout(timeoutId);
      console.error('❌ Fetch error (timeout or network):', fetchError);
      return {
        success: false,
        error: fetchError.name === 'AbortError' ? 'Request timeout' : fetchError.message,
        skipWorkspace: true
      };
    }

  } catch (error: any) {
    console.error('❌ Error creating Sphere Engine workspace with archive:', error);
    return {
      success: false,
      error: error.message,
      skipWorkspace: true
    };
  }
};

// New function for the enhanced build process with workspace archiving
/**
 * Unified initial build method that handles everything:
 * 1. Generate LLM response (if not exists)
 * 2. Process and save BuildResult
 * 3. Create workspace archive
 * 4. Upload to Sphere Engine
 * 5. Save to conversation history
 *
 * This replaces both llmController.generateLLMResponse and buildController.buildWithArchive
 * to eliminate race conditions and duplicates.
 */
export const unifiedInitialBuild = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid, prompt, projectData, userData }: GenerateCodeRequest = req.body;

    console.log('🚀 UNIFIED BUILD: Starting complete initial build workflow for:', interviewUuid);

    // Validate required fields
    if (!prompt && !interviewUuid) {
      return res.status(400).json({
        success: false,
        message: 'Either prompt or interviewUuid is required'
      });
    }

    // Get interview data if UUID provided
    let interviewData = null;
    let effectivePrompt = prompt;
    let effectiveProjectData = projectData;
    let effectiveUserData = userData;

    if (interviewUuid) {
      try {
        interviewData = await InterviewConfig.findOne({ uuid: interviewUuid });
        if (interviewData) {
          if (interviewData.user?.prompt) {
            effectivePrompt = interviewData.user.prompt;
          }
          if (interviewData.projectData) {
            effectiveProjectData = interviewData.projectData;
          }
          if (interviewData.user) {
            effectiveUserData = interviewData.user;
          }
        }
      } catch (error) {
        console.error('Error fetching interview data:', error);
      }
    }

    // Validate we have required data
    if (!effectivePrompt) {
      return res.status(400).json({
        success: false,
        message: 'No prompt available from request or interview data'
      });
    }

    if (!effectiveProjectData) {
      return res.status(400).json({
        success: false,
        message: 'No project data available from request or interview data'
      });
    }

    // Check if BuildResult already exists (avoid duplicates)
    let buildResult = null;
    if (interviewUuid) {
      buildResult = await BuildResult.findOne({
        interviewUuid: { $eq: interviewUuid, $ne: null, $exists: true }
      }).sort({ createdAt: -1 });

      if (buildResult) {
        console.log('✅ UNIFIED BUILD: Found existing BuildResult, preserving chat history');
        console.log('✅ UNIFIED BUILD: Existing chat history length:', buildResult.chatHistory?.length || 0);
        console.log('✅ UNIFIED BUILD: BuildResult status:', buildResult.status);
        console.log('✅ UNIFIED BUILD: BuildResult UUID:', buildResult.uuid);

        // If the build is already completed and has chat history, return it immediately
        if (buildResult.status === 'completed' && buildResult.chatHistory && buildResult.chatHistory.length > 0) {
          console.log('🚀 UNIFIED BUILD: Existing completed build found with chat history, returning immediately');

          // Return the existing build result without modification
          return res.json({
            success: true,
            message: 'Existing build result returned',
            buildResult: {
              uuid: buildResult.uuid,
              description: buildResult.description,
              deploymentInstructions: buildResult.deploymentInstructions,
              additionalSections: buildResult.additionalSections,
              status: buildResult.status,
              codeBlocks: buildResult.codeBlocks,
              projectStructure: buildResult.projectStructure,
              chatHistory: buildResult.chatHistory
            },
            workspace: null, // We don't need to recreate workspace for existing builds
            metadata: {
              interviewUuid,
              effectivePrompt,
              timestamp: new Date(),
              workflow: 'existing-build-returned'
            }
          });
        }
      }
    }

    // Step 1: Generate LLM response (only if not exists)
    if (!buildResult) {
      console.log('🤖 UNIFIED BUILD: Step 1 - Generating LLM response');

      const { content, metadata } = await AnthropicService.generateResponse(
        effectivePrompt,
        effectiveProjectData,
        effectiveUserData
      );

      console.log('✅ UNIFIED BUILD: LLM response generated, processing content...');

      // Step 2: Process and save BuildResult
      console.log('💾 UNIFIED BUILD: Step 2 - Processing and saving BuildResult');

      const buildServiceResult = await BuildService.processLLMResponse({
        interviewUuid,
        userId: req.user?.userId,
        rawLLMResponse: content,
        llmMetadata: metadata
      });

      if (!buildServiceResult.success || !buildServiceResult.buildResult) {
        return res.status(500).json({
          success: false,
          message: 'Failed to process LLM response',
          error: buildServiceResult.error
        });
      }

      buildResult = buildServiceResult.buildResult;

      // Step 3: Save to conversation history
      console.log('💬 UNIFIED BUILD: Step 3 - Saving to conversation history');

      try {
        const initialUserMessage = `Project Requirements:\n\nPrompt: ${effectivePrompt}\n\nProject Data:\n${JSON.stringify(effectiveProjectData, null, 2)}`;

        await ConversationHistoryService.saveMessagePair(
          interviewUuid!,
          initialUserMessage,
          content,
          {
            inputTokens: metadata.inputTokens,
            outputTokens: metadata.outputTokens,
            totalTokens: metadata.tokens,
            model: metadata.model
          }
        );

        console.log('✅ UNIFIED BUILD: Conversation history saved');
      } catch (conversationError) {
        console.error('❌ UNIFIED BUILD: Error saving conversation history:', conversationError);
        // Continue anyway - don't fail the build
      }
    }

    // Step 4: Create workspace archive
    console.log('📦 UNIFIED BUILD: Step 4 - Creating workspace archive');

    const archiveResult = await WorkspaceArchiver.createWorkspaceArchive(
      {
        description: buildResult.description,
        codeBlocks: buildResult.codeBlocks,
        projectStructure: buildResult.projectStructure,
        deploymentInstructions: buildResult.deploymentInstructions,
        additionalSections: buildResult.additionalSections
      },
      `workspace_${interviewUuid}`,
      interviewUuid!
    );

    if (!archiveResult.success || !archiveResult.archivePath) {
      return res.status(500).json({
        success: false,
        message: 'Failed to create workspace archive',
        error: archiveResult.error
      });
    }

    // Step 5: Create workspace and upload to Sphere Engine
    console.log('🌐 UNIFIED BUILD: Step 5 - Creating workspace and uploading to Sphere Engine');

    const workspaceResult = await getOrCreateWorkspaceWithArchive(
      archiveResult.archivePath,
      interviewUuid!,
      req.user?.userId || ''
    );

    // Clean up the archive file
    try {
      await fs.promises.unlink(archiveResult.archivePath);
      console.log(`🧹 UNIFIED BUILD: Cleaned up archive file`);
    } catch (cleanupError) {
      console.warn(`⚠️ UNIFIED BUILD: Failed to cleanup archive file:`, cleanupError);
    }

    // Step 6: Create and save initial chat history to BuildResult (only if no existing chat history)
    console.log('💬 UNIFIED BUILD: Step 6 - Creating and saving initial chat history');

    try {
      // Check if BuildResult already has chat history (don't overwrite existing)
      if (!buildResult.chatHistory || buildResult.chatHistory.length === 0) {
        console.log('💬 UNIFIED BUILD: No existing chat history, creating initial messages');

        // Create initial chat messages from the extracted content
        const initialChatHistory = createInitialChatHistory(buildResult);

        // Save chat history to BuildResult using atomic update
        const updatedBuildResult = await BuildResult.findOneAndUpdate(
          { uuid: buildResult.uuid },
          {
            chatHistory: initialChatHistory,
            status: 'completed' // Ensure status is completed
          },
          { new: true, runValidators: true }
        );

        if (!updatedBuildResult) {
          throw new Error('Failed to update BuildResult with chat history');
        }

        console.log('✅ UNIFIED BUILD: Initial chat history saved with', initialChatHistory.length, 'messages');
        buildResult = updatedBuildResult; // Use the updated result
      } else {
        console.log('✅ UNIFIED BUILD: Existing chat history found, preserving it with', buildResult.chatHistory.length, 'messages');

        // Just ensure status is completed
        const updatedBuildResult = await BuildResult.findOneAndUpdate(
          { uuid: buildResult.uuid },
          { status: 'completed' },
          { new: true, runValidators: true }
        );

        if (updatedBuildResult) {
          buildResult = updatedBuildResult;
        }
      }

    } catch (chatHistoryError) {
      console.error('❌ UNIFIED BUILD: Error handling chat history:', chatHistoryError);
      // Don't fail the entire build, but log the error
    }

    console.log('🎉 UNIFIED BUILD: Complete workflow finished successfully!');

    // Return comprehensive response with chat history included
    return res.json({
      success: true,
      message: 'Unified initial build completed successfully',
      buildResult: {
        uuid: buildResult.uuid,
        description: buildResult.description,
        deploymentInstructions: buildResult.deploymentInstructions,
        additionalSections: buildResult.additionalSections,
        status: buildResult.status,
        codeBlocks: buildResult.codeBlocks,
        projectStructure: buildResult.projectStructure,
        chatHistory: buildResult.chatHistory || [] // Include chat history in response
      },
      workspace: workspaceResult.success ? {
        workspaceId: workspaceResult.workspaceId,
        workspaceUrl: workspaceResult.workspaceUrl,
        status: workspaceResult.status
      } : null,
      metadata: {
        interviewUuid,
        effectivePrompt,
        timestamp: new Date(),
        workflow: 'unified-initial-build'
      }
    });

  } catch (error: any) {
    console.error('❌ UNIFIED BUILD: Error in unified initial build:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to complete unified initial build',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

export const buildWithArchive = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid, prompt, projectData, userData }: GenerateCodeRequest = req.body;

    console.log('🔥 DEBUG: buildWithArchive called with:', {
      interviewUuid,
      prompt,
      hasProjectData: !!projectData,
      hasUserData: !!userData,
      userId: req.user?.userId
    });

    // Get interview data if UUID provided
    let interviewData = null;
    let effectivePrompt = prompt;

    if (interviewUuid) {
      try {
        interviewData = await InterviewConfig.findOne({ uuid: interviewUuid });
        if (interviewData?.user?.prompt) {
          effectivePrompt = interviewData.user.prompt;
        }
      } catch (error) {
        console.error('Error fetching interview data:', error);
      }
    }

    // Check if BuildResult already exists for this interview
    if (interviewUuid) {
      console.log('🔍 DEBUG: Checking for existing BuildResult for interview:', interviewUuid);

      const existingBuildResult = await BuildResult.findOne({
        interviewUuid: { $eq: interviewUuid, $ne: null, $exists: true }
      }).sort({ createdAt: -1 });

      if (existingBuildResult) {
        console.log('✅ DEBUG: Found existing BuildResult, proceeding with archive creation');

        // Create workspace archive from existing build result
        const archiveResult = await WorkspaceArchiver.createWorkspaceArchive(
          {
            description: existingBuildResult.description,
            codeBlocks: existingBuildResult.codeBlocks,
            projectStructure: existingBuildResult.projectStructure,
            deploymentInstructions: existingBuildResult.deploymentInstructions,
            additionalSections: existingBuildResult.additionalSections
          },
          `workspace_${interviewUuid}`,
          interviewUuid
        );

        if (archiveResult.success && archiveResult.archivePath) {
          // FIXED: Check for existing workspace first, then create if needed
          const workspaceResult = await getOrCreateWorkspaceWithArchive(
            archiveResult.archivePath,
            interviewUuid,
            req.user?.userId || ''
          );

          // Clean up the unique archive file after use
          try {
            await fs.promises.unlink(archiveResult.archivePath);
            console.log(`🧹 Cleaned up archive file: ${archiveResult.archivePath}`);
          } catch (cleanupError) {
            console.warn(`⚠️ Failed to cleanup archive file: ${cleanupError}`);
          }

          return res.json({
            success: true,
            buildResult: {
              uuid: existingBuildResult.uuid,
              description: existingBuildResult.description,
              deploymentInstructions: existingBuildResult.deploymentInstructions,
              additionalSections: existingBuildResult.additionalSections,
              status: existingBuildResult.status
            },
            projectStructure: existingBuildResult.projectStructure,
            archive: {
              path: archiveResult.archivePath,
              structureJsonPath: archiveResult.structureJsonPath
            },
            workspace: workspaceResult
          });
        } else {
          return res.status(500).json({
            success: false,
            message: 'Failed to create workspace archive',
            error: archiveResult.error
          });
        }
      }

      // No existing BuildResult found, create one now
      console.log('🆕 No existing BuildResult found, creating new one for interview:', interviewUuid);

      // Validate that we have the required data
      if (!effectivePrompt) {
        return res.status(400).json({
          success: false,
          message: 'No prompt available from request or interview data'
        });
      }

      let effectiveProjectData = projectData;
      if (interviewData?.projectData) {
        effectiveProjectData = interviewData.projectData;
      }

      if (!effectiveProjectData) {
        return res.status(400).json({
          success: false,
          message: 'No project data available from request or interview data'
        });
      }

      // Skip API configuration check in mock mode
      console.log('🧪 MOCK MODE: Skipping Anthropic API configuration check');

      console.log('🚀 Generating LLM response using Anthropic API for buildWithArchive...');

      // Generate response using Anthropic API
      const { content, metadata } = await AnthropicService.generateResponse(
        effectivePrompt,
        effectiveProjectData,
        userData
      );

      console.log('✅ Anthropic API response generated successfully, processing content...');

      // Create BuildResult using BuildService
      const buildResult = await BuildService.processLLMResponse({
        interviewUuid,
        userId: req.user?.userId,
        rawLLMResponse: content,
        llmMetadata: metadata
      });

      if (buildResult.success && buildResult.buildResult) {
        console.log('✅ Created new BuildResult successfully');

        // Save the initial project build to conversation history
        try {
          console.log('💾 Saving initial project build to conversation history for interview:', interviewUuid);

          // Format the project data and prompt as the initial user message
          const initialUserMessage = `Project Requirements:\n\nPrompt: ${prompt}\n\nProject Data:\n${JSON.stringify(projectData, null, 2)}`;

          await ConversationHistoryService.saveMessagePair(
            interviewUuid,
            initialUserMessage,
            content,
            {
              inputTokens: metadata.inputTokens,
              outputTokens: metadata.outputTokens,
              totalTokens: metadata.tokens,
              model: metadata.model
            }
          );

          console.log('✅ Initial project build saved to conversation history');
        } catch (conversationError) {
          console.error('❌ Error saving initial project build to conversation history:', conversationError);
          // Don't fail the request if conversation saving fails
        }

        // Create workspace archive from new build result
        const archiveResult = await WorkspaceArchiver.createWorkspaceArchive(
          {
            description: buildResult.buildResult.description,
            codeBlocks: buildResult.buildResult.codeBlocks,
            projectStructure: buildResult.buildResult.projectStructure,
            deploymentInstructions: buildResult.buildResult.deploymentInstructions,
            additionalSections: buildResult.buildResult.additionalSections
          },
          `workspace_${interviewUuid}`,
          interviewUuid
        );

        if (archiveResult.success && archiveResult.archivePath) {
          // Create workspace using Sphere Engine API with archive
          const workspaceResult = await createSphereEngineWorkspaceWithArchive(
            archiveResult.archivePath,
            interviewUuid,
            req.user?.userId || ''
          );

          // Clean up the unique archive file after use
          try {
            await fs.promises.unlink(archiveResult.archivePath);
            console.log(`🧹 Cleaned up archive file: ${archiveResult.archivePath}`);
          } catch (cleanupError) {
            console.warn(`⚠️ Failed to cleanup archive file: ${cleanupError}`);
          }

          return res.json({
            success: true,
            buildResult: {
              uuid: buildResult.buildResult.uuid,
              description: buildResult.buildResult.description,
              deploymentInstructions: buildResult.buildResult.deploymentInstructions,
              additionalSections: buildResult.buildResult.additionalSections
            },
            projectStructure: buildResult.buildResult.projectStructure,
            archive: {
              path: archiveResult.archivePath,
              structureJsonPath: archiveResult.structureJsonPath
            },
            workspace: workspaceResult
          });
        } else {
          return res.status(500).json({
            success: false,
            message: 'Failed to create workspace archive',
            error: archiveResult.error
          });
        }
      } else {
        console.error('❌ Failed to create BuildResult:', buildResult.error);
        return res.status(500).json({
          success: false,
          message: 'Failed to create build result',
          error: buildResult.error
        });
      }
    }

    // Fallback for cases without interviewUuid
    return res.status(400).json({
      success: false,
      message: 'Interview UUID is required for the new build process'
    });

  } catch (error: any) {
    console.error('Error in buildWithArchive:', error);
    res.status(500).json({
      success: false,
      message: 'Error in build process with archive',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// New function to create workspace from existing archive
export const createWorkspaceFromArchive = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { archivePath, interviewUuid } = req.body;

    console.log('🔥 DEBUG: createWorkspaceFromArchive called with:', {
      archivePath,
      interviewUuid,
      userId: req.user?.userId
    });

    if (!archivePath) {
      return res.status(400).json({
        success: false,
        message: 'Archive path is required'
      });
    }

    if (!interviewUuid) {
      return res.status(400).json({
        success: false,
        message: 'Interview UUID is required'
      });
    }

    // Create workspace using Sphere Engine API with archive
    const workspaceResult = await createSphereEngineWorkspaceWithArchive(
      archivePath,
      interviewUuid,
      req.user?.userId || ''
    );

    return res.json({
      success: workspaceResult.success,
      workspace: workspaceResult.workspace,
      message: workspaceResult.message,
      error: workspaceResult.error,
      details: workspaceResult.details
    });

  } catch (error: any) {
    console.error('Error in createWorkspaceFromArchive:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating workspace from archive',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// New function to get code structure without chat messages
export const getCodeStructure = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid, prompt, projectData, userData }: GenerateCodeRequest = req.body;

    console.log('🔥 DEBUG: getCodeStructure called with:', {
      interviewUuid,
      prompt,
      hasProjectData: !!projectData,
      hasUserData: !!userData,
      userId: req.user?.userId
    });

    // Get interview data if UUID provided
    let interviewData = null;
    let effectivePrompt = prompt;

    if (interviewUuid) {
      try {
        interviewData = await InterviewConfig.findOne({ uuid: interviewUuid });
        if (interviewData?.user?.prompt) {
          effectivePrompt = interviewData.user.prompt;
        }
      } catch (error) {
        console.error('Error fetching interview data:', error);
      }
    }

    // FIXED: Check if BuildResult already exists for this interview
    if (interviewUuid) {
      console.log('🔍 DEBUG: Checking for existing BuildResult for interview:', interviewUuid);

      // First, try to find ANY BuildResult for this interview (to prevent duplicates)
      const existingBuildResult = await BuildResult.findOne({
        interviewUuid: { $eq: interviewUuid, $ne: null, $exists: true }
      }).sort({ createdAt: -1 });

      console.log('🔍 DEBUG: Found existing BuildResult:', !!existingBuildResult);
      if (existingBuildResult) {
        console.log('🔍 DEBUG: Existing BuildResult status:', existingBuildResult.status);
        console.log('🔍 DEBUG: Existing BuildResult UUID:', existingBuildResult.uuid);
      }

      // If we found ANY BuildResult for this interview, return it (even if processing/failed)
      if (existingBuildResult) {
        console.log('✅ DEBUG: Returning existing BuildResult to prevent duplicates');
        return res.json({
          success: true,
          projectStructure: existingBuildResult.projectStructure,
          buildResult: {
            uuid: existingBuildResult.uuid,
            description: existingBuildResult.description,
            deploymentInstructions: existingBuildResult.deploymentInstructions,
            additionalSections: existingBuildResult.additionalSections,
            status: existingBuildResult.status
          }
        });
      }

      // No existing BuildResult found, create one now
      console.log('🆕 No existing BuildResult found, creating new one for interview:', interviewUuid);

      // Validate that we have the required data
      if (!effectivePrompt) {
        return res.status(400).json({
          success: false,
          message: 'No prompt available from request or interview data'
        });
      }

      let effectiveProjectData = projectData;
      if (interviewData?.projectData) {
        effectiveProjectData = interviewData.projectData;
      }

      if (!effectiveProjectData) {
        return res.status(400).json({
          success: false,
          message: 'No project data available from request or interview data'
        });
      }

      // Skip API configuration check in mock mode
      console.log('🧪 MOCK MODE: Skipping Anthropic API configuration check');

      console.log('🚀 Generating LLM response using Anthropic API for getCodeStructure...');

      // Generate response using Anthropic API
      const { content, metadata } = await AnthropicService.generateResponse(
        effectivePrompt,
        effectiveProjectData,
        userData
      );

      console.log('✅ Anthropic API response generated successfully, processing content...');

      // Create BuildResult using BuildService
      const buildResult = await BuildService.processLLMResponse({
        interviewUuid,
        userId: req.user?.userId,
        rawLLMResponse: content,
        llmMetadata: metadata
      });

      if (buildResult.success && buildResult.buildResult) {
        console.log('✅ Created new BuildResult successfully');
        return res.json({
          success: true,
          projectStructure: buildResult.buildResult.projectStructure,
          buildResult: {
            uuid: buildResult.buildResult.uuid,
            description: buildResult.buildResult.description,
            deploymentInstructions: buildResult.buildResult.deploymentInstructions,
            additionalSections: buildResult.buildResult.additionalSections
          }
        });
      } else {
        console.error('❌ Failed to create BuildResult:', buildResult.error);
        return res.status(500).json({
          success: false,
          message: 'Failed to create build result',
          error: buildResult.error
        });
      }
    }

    // Fallback: Generate the project structure using extracted real code (for cases without interviewUuid)
    const projectStructure = await CodeExtractor.getCodeStructureFromFile('doc/generated_code2');

    // Log the data structure being sent to frontend
    // console.log('=== PROJECT STRUCTURE BEING SENT TO FRONTEND ===');
    // console.log('Project structure type:', typeof projectStructure);
    // console.log('Project structure keys:', Object.keys(projectStructure));
    // console.log('=== END PROJECT STRUCTURE ===');

    // Return the code structure directly
    res.json({
      success: true,
      projectStructure,
      message: 'Code structure retrieved successfully',
      metadata: {
        effectivePrompt,
        interviewUuid,
        timestamp: new Date()
      }
    });

  } catch (error: any) {
    console.error('Error getting code structure:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving code structure',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

export const generateCode = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { interviewUuid, prompt, projectData, userData }: GenerateCodeRequest = req.body;

    // Set up SSE headers for streaming response
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // Function to send SSE data
    const sendEvent = (type: string, data: any) => {
      res.write(`event: ${type}\n`);
      res.write(`data: ${JSON.stringify(data)}\n\n`);
    };

    // Send initial message
    sendEvent('message', {
      type: 'ai',
      content: '🚀 Starting code generation based on your requirements...',
      timestamp: new Date()
    });

    await new Promise(resolve => setTimeout(resolve, 1000));

    // Get interview data if UUID provided
    let interviewData = null;
    let effectivePrompt = prompt;
    
    if (interviewUuid) {
      try {
        interviewData = await InterviewConfig.findOne({ uuid: interviewUuid });
        if (interviewData?.user?.prompt) {
          effectivePrompt = interviewData.user.prompt;
        }
      } catch (error) {
        console.error('Error fetching interview data:', error);
      }
    }

    sendEvent('message', {
      type: 'ai',
      content: '✅ Code generation complete! Your project is ready. Use the separate /api/build/code endpoint to retrieve the generated files and project structure.',
      timestamp: new Date()
    });

    // Send completion signal without code structure
    sendEvent('generationComplete', {
      success: true,
      message: 'Code generation process completed',
      metadata: {
        effectivePrompt,
        interviewUuid,
        timestamp: new Date()
      }
    });

    // End the stream
    sendEvent('end', { message: 'Generation complete' });
    res.end();

  } catch (error: any) {
    console.error('Error generating code:', error);
    
    // Send error event
    res.write(`event: error\n`);
    res.write(`data: ${JSON.stringify({
      success: false,
      message: 'Error generating code',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })}\n\n`);
    
    res.end();
  }
};

// Handle chat messages
export const handleChat = async (req: AuthenticatedRequest, res: Response) => {
  console.log('🔥 Chat endpoint hit with:', { interviewUuid: req.body.interviewUuid, message: req.body.message?.substring(0, 50) });
  try {
    const { interviewUuid, message, conversationHistory }: ChatRequest = req.body;

    if (!message.trim()) {
      return res.status(400).json({
        success: false,
        message: 'Message is required'
      });
    }

    // FIXED: Find the BuildResult for this interview to save chat history
    console.log('🔍 Looking for BuildResult with interviewUuid:', interviewUuid);

    let buildResult = await BuildResult.findOne({
      interviewUuid: { $eq: interviewUuid, $ne: null, $exists: true },
      status: 'completed'
    }).sort({ createdAt: -1 });

    console.log('🔍 Found completed BuildResult:', !!buildResult);

    // If no completed build found, try to find any build for this interview
    if (!buildResult) {
      buildResult = await BuildResult.findOne({
        interviewUuid: { $eq: interviewUuid, $ne: null, $exists: true }
      }).sort({ createdAt: -1 });
      console.log('🔍 Found any BuildResult:', !!buildResult);
      if (buildResult) {
        console.log('🔍 BuildResult status:', buildResult.status);
        console.log('🔍 BuildResult uuid:', buildResult.uuid);
      }
    }

    if (!buildResult) {
      console.log('❌ No BuildResult found for interview:', interviewUuid);
      return res.status(404).json({
        success: false,
        message: 'No build found for this interview. Please complete the initial code generation first.'
      });
    }

    // FIXED: Create user message object
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: message,
      timestamp: new Date()
    };

    // Generate AI response based on user message with conversation context
    let aiResponseData: { fullResponse: string; chatSummary: string };
    try {
      aiResponseData = await generateAIResponse(message, interviewUuid, buildResult);
    } catch (aiError: any) {
      console.error('❌ AI response generation failed:', aiError);

      // Return proper error response to user instead of hiding the error
      return res.status(500).json({
        success: false,
        message: 'Failed to generate AI response',
        error: aiError.message || 'Unable to process your request at this time'
      });
    }

    // FIXED: Create AI message object using the chat summary for display
    const aiMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      type: 'ai',
      content: aiResponseData.chatSummary, // Use summary for chatbox display
      timestamp: new Date()
    };

    // Save conversation context for LLM API continuity (use full response for context)
    try {
      console.log('💾 Saving conversation context for interview:', interviewUuid);
      await ConversationHistoryService.saveMessagePair(
        interviewUuid,
        message,
        aiResponseData.fullResponse // Save full response for LLM context
      );
      console.log('✅ Conversation context saved successfully');
    } catch (contextError) {
      console.error('❌ Error saving conversation context:', contextError);
      // Don't fail the request if context saving fails
    }

    // FIXED: Save both user message and AI response to BuildResult using atomic update
    try {
      const result = await BuildResult.findOneAndUpdate(
        { uuid: buildResult.uuid },
        {
          $push: {
            chatHistory: {
              $each: [userMessage, aiMessage]
            }
          }
        },
        { new: true }
      );

      if (result) {
        console.log('💾 Chat messages saved to BuildResult:', buildResult.uuid);
        console.log('💾 Total messages now:', result.chatHistory.length);
      } else {
        console.error('❌ BuildResult not found for atomic update');
      }
    } catch (saveError) {
      console.error('❌ Error saving chat messages:', saveError);
    }

    // FIXED: Return simple JSON response instead of streaming
    res.json({
      success: true,
      message: aiResponseData.chatSummary, // Return summary for frontend display
      aiMessage: aiMessage,
      userMessage: userMessage,
      fullResponse: aiResponseData.fullResponse // Include full response for debugging/logging
    });

  } catch (error: any) {
    console.error('Error handling chat:', error);
    
    res.write(`event: error\n`);
    res.write(`data: ${JSON.stringify({
      success: false,
      message: 'Error processing chat message',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    })}\n\n`);
    
    res.end();
  }
};

const generateAIResponse = async (userMessage: string, interviewUuid: string, buildResult?: any): Promise<{
  fullResponse: string;
  chatSummary: string;
}> => {
  console.log('🤖 Generating AI response for message:', userMessage.substring(0, 50));

  try {
    // Get complete conversation context for LLM API
    let conversationMessages;
    try {
      conversationMessages = await ConversationHistoryService.getCompleteConversationContext(
        interviewUuid,
        userMessage
      );
      console.log('📖 Retrieved conversation context:', {
        messageCount: conversationMessages.length,
        hasHistory: conversationMessages.length > 1
      });

      // Show what the Anthropic API payload would look like
      const anthropicPayload = {
        model: 'claude-sonnet-4-20250514',
        max_tokens: 64000,
        messages: conversationMessages
      };

      console.log('🚀 ANTHROPIC API PAYLOAD (CHAT - MOCK MODE):', {
        model: anthropicPayload.model,
        max_tokens: anthropicPayload.max_tokens,
        messageCount: anthropicPayload.messages.length,
        totalContextLength: conversationMessages.reduce((sum, m) => sum + m.content.length, 0)
      });

      console.log('📋 FULL CHAT PAYLOAD CONTENT:');
      console.log(JSON.stringify(anthropicPayload, null, 2));

    } catch (contextError) {
      console.error('❌ Error getting conversation context:', contextError);
      // Fallback to just current message
      conversationMessages = [{ role: 'user' as const, content: userMessage }];
    }

    // Check if we should use mock mode for chat messages
    const bypassApiKey = process.env.BYPASS_API_KEY === 'true';

    if (bypassApiKey) {
      // Mock mode: use doc/llm_reply.json
      console.log('🧪 MOCK MODE: Using doc/llm_reply.json for chat response');

      const llmReplyPath = path.join(process.cwd(), '..', 'doc', 'llm_reply.json');
      let mockResponse: any;

      try {
        const llmReplyContent = await fs.promises.readFile(llmReplyPath, 'utf-8');
        mockResponse = JSON.parse(llmReplyContent);
        console.log('📄 LLM reply mock loaded from doc/llm_reply.json');
      } catch (error) {
        console.error('❌ Error reading llm_reply.json file:', error);
        const fallbackMessage = "I understand! Let me help you with that. I'll analyze your request and make the appropriate changes to your codebase.";
        return {
          fullResponse: fallbackMessage,
          chatSummary: fallbackMessage
        };
      }

      // Extract the text content from the mock response
      let responseText = '';
      if (mockResponse.content && Array.isArray(mockResponse.content)) {
        responseText = mockResponse.content
          .filter((item: any) => item.type === 'text')
          .map((item: any) => item.text)
          .join('\n');
      }

      if (!responseText) {
        console.error('❌ No text content found in mock response');
        const fallbackMessage = "I understand! Let me help you with that. I'll analyze your request and make the appropriate changes to your codebase.";
        return {
          fullResponse: fallbackMessage,
          chatSummary: fallbackMessage
        };
      }

      // Extract content using CodeExtractor for potential file uploads (same as before)
      try {
        const extractedContent = CodeExtractor.extractFullContent(responseText);
        console.log('🔍 Extracted content from mock response:', {
          hasDescription: !!extractedContent.description,
          codeBlocksCount: extractedContent.codeBlocks.length,
          hasProjectStructure: !!extractedContent.projectStructure,
          hasDeploymentInstructions: !!extractedContent.deploymentInstructions
        });

        // If we have a build result with workspace info, upload the extracted structure
        if (buildResult && extractedContent.projectStructure) {
          await uploadExtractedStructureToWorkspace(buildResult, extractedContent);
        }
      } catch (extractionError) {
        console.error('❌ Error extracting content from mock response:', extractionError);
        // Continue anyway - don't fail the chat response
      }

      // Create a concise summary for the chatbox
      const chatSummary = createChatSummary(responseText, userMessage);

      console.log('📝 Generated chat summary:', chatSummary);
      console.log('📄 Full response length:', responseText.length, 'characters');

      return {
        fullResponse: responseText,
        chatSummary: chatSummary
      };
    } else {
      // Production mode: make real API call with conversation context
      console.log('🚀 PRODUCTION MODE: Making real Anthropic API call with conversation context');

      try {
        // Make real API call with conversation context
        const anthropicPayload = {
          model: 'claude-sonnet-4-20250514',
          max_tokens: 64000,
          messages: conversationMessages
        };

        const apiKey = process.env.ANTHROPIC_API_KEY;
        if (!apiKey || apiKey === 'your_api_key') {
          throw new Error('ANTHROPIC_API_KEY is not properly configured');
        }

        const response = await fetch('https://api.anthropic.com/v1/messages', {
          method: 'POST',
          headers: {
            'x-api-key': apiKey,
            'anthropic-version': '2023-06-01',
            'content-type': 'application/json'
          },
          body: JSON.stringify(anthropicPayload)
        });

        if (!response.ok) {
          throw new Error(`Anthropic API error: ${response.status} ${response.statusText}`);
        }

        const apiResponse = await response.json();

        // Extract text content from response
        const responseText = apiResponse.content
          .filter((item: any) => item.type === 'text')
          .map((item: any) => item.text)
          .join('\n');

        console.log('✅ Real Anthropic API response received:', {
          id: apiResponse.id,
          model: apiResponse.model,
          inputTokens: apiResponse.usage?.input_tokens,
          outputTokens: apiResponse.usage?.output_tokens
        });

        // Extract content using CodeExtractor for potential file uploads (same as mock mode)
        try {
          const extractedContent = CodeExtractor.extractFullContent(responseText);
          console.log('🔍 Extracted content from real API response:', {
            hasDescription: !!extractedContent.description,
            codeBlocksCount: extractedContent.codeBlocks.length,
            hasProjectStructure: !!extractedContent.projectStructure,
            hasDeploymentInstructions: !!extractedContent.deploymentInstructions
          });

          // If we have a build result with workspace info, upload the extracted structure
          if (buildResult && extractedContent.projectStructure) {
            await uploadExtractedStructureToWorkspace(buildResult, extractedContent);
          }
        } catch (extractionError) {
          console.error('❌ Error extracting content from real API response:', extractionError);
          // Continue anyway - don't fail the chat response
        }

        // Create a concise summary for the chatbox
        const chatSummary = createChatSummary(responseText, userMessage);

        console.log('📝 Generated chat summary (production):', chatSummary);
        console.log('📄 Full response length (production):', responseText.length, 'characters');

        return {
          fullResponse: responseText,
          chatSummary: chatSummary
        };

      } catch (apiError: any) {
        console.error('❌ Real API call failed:', apiError);

        // Provide specific error messages instead of generic responses
        if (apiError.message?.includes('401')) {
          throw new Error('Invalid Anthropic API key. Please check your configuration.');
        } else if (apiError.message?.includes('429')) {
          throw new Error('API rate limit exceeded. Please try again later.');
        } else if (apiError.message?.includes('timeout')) {
          throw new Error('Request timed out. Please try again.');
        } else if (apiError.message?.includes('network') || apiError.message?.includes('ENOTFOUND')) {
          throw new Error('Network error. Please check your internet connection.');
        } else {
          throw new Error(`API error: ${apiError.message || 'Unable to process your request'}`);
        }
      }
    }

  } catch (error: any) {
    console.error('❌ Error in generateAIResponse:', error);

    // Re-throw the error to be handled by the calling function
    // This ensures proper error propagation instead of hiding errors with generic responses
    throw error;
  }
};

// Helper function to upload extracted structure to workspace
const uploadExtractedStructureToWorkspace = async (buildResult: any, extractedContent: any) => {
  try {
    console.log('📤 Uploading extracted structure to workspace...');

    // Get workspace ID from buildResult or find associated workspace
    let workspaceId = null;

    // Try to find workspace from database
    if (buildResult.interviewUuid) {
      const workspace = await Workspace.findOne({
        interviewUuid: buildResult.interviewUuid
      }).sort({ createdAt: -1 });

      if (workspace && workspace.sphereEngineWorkspaceId) {
        workspaceId = workspace.sphereEngineWorkspaceId;
        console.log('🔍 Found workspace ID:', workspaceId);
      }
    }

    if (!workspaceId) {
      console.warn('⚠️ No workspace ID found, skipping file upload');
      return;
    }

    // Get Sphere Engine configuration
    const config = {
      customerId: process.env.SPHERE_ENGINE_CUSTOMER_ID || '',
      accessToken: process.env.SPHERE_ENGINE_ACCESS_TOKEN || '',
      apiEndpoint: process.env.SPHERE_ENGINE_API_ENDPOINT || ''
    };

    if (!config.accessToken || !config.apiEndpoint) {
      console.warn('⚠️ Sphere Engine configuration missing, skipping file upload');
      return;
    }

    // Create extracted_structure.json file with only the projectStructure content
    const structureContent = JSON.stringify(extractedContent.projectStructure || {}, null, 2);

    // Upload the structure file using the specified API format
    const formData = new FormData();
    formData.append('filedata', structureContent, 'extracted_structure.json');
    formData.append('filepath', '.structure/extracted_structure.json');

    const uploadUrl = `https://${config.apiEndpoint}/api/v1/workspaces/${workspaceId}/files`;
    console.log('📤 Uploading to:', uploadUrl);

    const response = await fetch(uploadUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.accessToken}`
      },
      body: formData
    });

    if (response.ok) {
      console.log('✅ Successfully uploaded extracted_structure.json to workspace');
    } else {
      const errorText = await response.text();
      console.error('❌ Failed to upload structure file:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
    }

  } catch (error) {
    console.error('❌ Error uploading extracted structure to workspace:', error);
  }
};

