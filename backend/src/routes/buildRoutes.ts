import express from 'express';
import {
  generateCode,
  handleChat,
  getCodeStructure,
  buildWithArchive,
  createWorkspaceFromArchive,
  unifiedInitialBuild
} from '../controllers/buildController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// POST /api/build/unified - Unified initial build (replaces llm/generate + build/archive)
router.post('/unified', authenticateToken, unifiedInitialBuild);

// POST /api/build/code - Get code structure without chat messages
router.post('/code', authenticateToken, getCodeStructure);

// POST /api/build/archive - New build process with workspace archiving (legacy)
router.post('/archive', authenticateToken, buildWithArchive);

// POST /api/build/workspace - Create workspace from existing archive
router.post('/workspace', authenticateToken, createWorkspaceFromArchive);

// POST /api/build/generate - Generate code with streaming chat messages
router.post('/generate', authenticateToken, generateCode);

// POST /api/build/chat - Handle chat messages with AI
router.post('/chat', authenticateToken, handleChat);

export default router;