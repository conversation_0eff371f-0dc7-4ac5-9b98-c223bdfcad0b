import express from 'express';
import {
  getWorkspaceStructure,
  readWorkspace<PERSON><PERSON>,
  writeWorkspaceFile,
  deleteWorkspaceFile,
  createWorkspace,
  listWorkspaces
} from '../controllers/workspaceController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// GET /api/workspace - List all workspaces
router.get('/', authenticateToken, listWorkspaces);

// POST /api/workspace - Create a new workspace
router.post('/', authenticateToken, createWorkspace);

// GET /api/workspace/:workspaceName/structure - Get workspace file structure
router.get('/:workspaceName/structure', authenticateToken, getWorkspaceStructure);

// POST /api/workspace/:workspaceName/files/read - Read a specific file
router.post('/:workspaceName/files/read', authenticateToken, readWorkspaceFile);

// POST /api/workspace/:workspaceName/files/write - Write/update a file
router.post('/:workspaceName/files/write', authenticateToken, writeWorkspaceFile);

// DELETE /api/workspace/:workspaceName/files - Delete a file
router.delete('/:workspaceName/files', authenticateToken, deleteWorkspaceFile);

export default router; 