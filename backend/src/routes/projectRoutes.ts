import express from 'express';
import {
  saveProject,
  getProjectByInterview,
  updateFile,
  deleteFile,
  getUserProjects
} from '../controllers/projectController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// POST /api/projects - Create a new project
router.post('/', authenticateToken, saveProject);

// GET /api/projects/interview/:interviewUuid - Get project by interview UUID
router.get('/interview/:interviewUuid', authenticateToken, getProjectByInterview);

// PUT /api/projects/:projectUuid/files - Update a file in the project
router.put('/:projectUuid/files', authenticateToken, updateFile);

// DELETE /api/projects/:projectUuid/files - Delete a file from the project
router.delete('/:projectUuid/files', authenticateToken, deleteFile);

// GET /api/projects/user/:userId - Get all projects for a user
router.get('/user/:userId', authenticateToken, getUserProjects);

export default router; 