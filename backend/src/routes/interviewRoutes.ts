import express from 'express';
import {
  saveInterviewConfig,
  getInterviewConfig,
  getUserInterviewConfigs,
  getUserHistory
} from '../controllers/interviewController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// POST /api/interview - Save a new interview configuration
router.post('/', authenticateToken, saveInterviewConfig);

// GET /api/interview/:uuid - Get interview configuration by UUID
router.get('/:uuid', authenticateToken, getInterviewConfig);

// GET /api/interview/user/:userId - Get all interview configurations for a user
router.get('/user/:userId', authenticateToken, getUserInterviewConfigs);

// GET /api/interview/history/:userId - Get simplified history for a user
router.get('/history/:userId', authenticateToken, getUserHistory);

export default router;