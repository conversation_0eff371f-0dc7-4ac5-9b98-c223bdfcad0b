{"name": "mergen-backend", "version": "1.0.0", "description": "Backend server for Mergen", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon src/server.ts", "build": "tsc", "test": "jest", "lint": "eslint . --ext .ts"}, "dependencies": {"@types/form-data": "^2.2.1", "@types/uuid": "^10.0.0", "axios": "^1.11.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.3", "form-data": "^4.0.4", "jsonwebtoken": "^9.0.2", "mongoose": "^8.2.0", "node-fetch": "^2.7.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20.11.24", "@types/node-fetch": "^2.6.12", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "eslint": "^8.57.0", "jest": "^29.7.0", "nodemon": "^3.1.0", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}